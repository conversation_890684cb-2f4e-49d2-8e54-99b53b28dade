{"ast": null, "code": "var _jsxFileName = \"D:\\\\Softwares\\\\insyt-care\\\\insyt-care-web\\\\src\\\\components\\\\NotificationSettings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Switch, FormControlLabel, Button, Alert, CircularProgress, Divider } from '@mui/material';\nimport { useSelector } from 'react-redux';\nimport { doc, updateDoc } from 'firebase/firestore';\nimport { db, messaging } from '@config/firebase.config';\nimport { COLLECTIONS } from '@constants/app';\nimport { getToken, isSupported } from 'firebase/messaging';\nimport { useSnackbar } from 'notistack';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotificationSettings = () => {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const [pushNotifications, setPushNotifications] = useState(true);\n  const [browserPermission, setBrowserPermission] = useState('default');\n  const [loading, setLoading] = useState(false);\n  const [fcmSupported, setFcmSupported] = useState(false);\n  useEffect(() => {\n    // Check if FCM is supported\n    isSupported().then(setFcmSupported);\n\n    // Check browser notification permission\n    setBrowserPermission(Notification.permission);\n\n    // Set initial state from user data\n    if (user) {\n      setPushNotifications(user.pushNotification !== false); // Default to true\n    }\n  }, [user]);\n  const handlePushNotificationToggle = async event => {\n    const enabled = event.target.checked;\n    setLoading(true);\n    try {\n      if (enabled) {\n        // If enabling notifications, request permission and get FCM token\n        if (Notification.permission !== 'granted') {\n          const permission = await Notification.requestPermission();\n          setBrowserPermission(permission);\n          if (permission !== 'granted') {\n            enqueueSnackbar('Browser notification permission is required for push notifications.', {\n              variant: 'warning'\n            });\n            setLoading(false);\n            return;\n          }\n        }\n\n        // Get FCM token\n        if (fcmSupported) {\n          const vapidKey = process.env.REACT_APP_FIREBASE_WEB_PUSH;\n          if (!vapidKey) {\n            enqueueSnackbar('Push notification configuration is incomplete. Please contact administrator.', {\n              variant: 'error'\n            });\n            setLoading(false);\n            return;\n          }\n          const token = await getToken(messaging, {\n            vapidKey\n          });\n          if (token) {\n            // Update user document with FCM token and notification preference\n            await updateDoc(doc(db, COLLECTIONS.USERS, user.id), {\n              pushNotification: true,\n              fcmToken: token,\n              notificationUpdatedAt: new Date()\n            });\n            setPushNotifications(true);\n            enqueueSnackbar('Push notifications enabled successfully!', {\n              variant: 'success'\n            });\n          } else {\n            throw new Error('Unable to get FCM token');\n          }\n        }\n      } else {\n        // If disabling notifications, just update the preference\n        await updateDoc(doc(db, COLLECTIONS.USERS, user.id), {\n          pushNotification: false,\n          notificationUpdatedAt: new Date()\n        });\n        setPushNotifications(false);\n        enqueueSnackbar('Push notifications disabled.', {\n          variant: 'info'\n        });\n      }\n    } catch (error) {\n      console.error('Error updating notification settings:', error);\n      enqueueSnackbar('Failed to update notification settings. Please try again.', {\n        variant: 'error'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const requestBrowserPermission = async () => {\n    try {\n      const permission = await Notification.requestPermission();\n      setBrowserPermission(permission);\n      if (permission === 'granted') {\n        enqueueSnackbar('Browser notification permission granted!', {\n          variant: 'success'\n        });\n      } else {\n        enqueueSnackbar('Browser notification permission denied.', {\n          variant: 'warning'\n        });\n      }\n    } catch (error) {\n      console.error('Error requesting permission:', error);\n      enqueueSnackbar('Error requesting notification permission.', {\n        variant: 'error'\n      });\n    }\n  };\n  if (!fcmSupported) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Notification Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Push notifications are not supported in this browser.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Notification Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          gutterBottom: true,\n          children: \"Manage your push notification preferences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          children: \"Browser Permission Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [browserPermission === 'granted' && '✅ Granted', browserPermission === 'denied' && '❌ Denied', browserPermission === 'default' && '⏳ Not requested']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), browserPermission !== 'granted' && /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            variant: \"outlined\",\n            onClick: requestBrowserPermission,\n            children: \"Request Permission\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Push Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Receive notifications for new messages, appointments, and updates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Switch, {\n            checked: pushNotifications,\n            onChange: handlePushNotificationToggle,\n            disabled: loading || browserPermission === 'denied'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this),\n          label: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1,\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Updating settings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this), browserPermission === 'denied' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mt: 2\n        },\n        children: \"Browser notifications are blocked. To enable push notifications, please allow notifications in your browser settings and refresh the page.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n_s(NotificationSettings, \"KutoBXpztWc3OuiJbRgt77UZyJQ=\", false, function () {\n  return [useSelector, useSnackbar];\n});\n_c = NotificationSettings;\nexport default NotificationSettings;\nvar _c;\n$RefreshReg$(_c, \"NotificationSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Switch", "FormControlLabel", "<PERSON><PERSON>", "<PERSON><PERSON>", "CircularProgress", "Divider", "useSelector", "doc", "updateDoc", "db", "messaging", "COLLECTIONS", "getToken", "isSupported", "useSnackbar", "jsxDEV", "_jsxDEV", "NotificationSettings", "_s", "user", "state", "auth", "enqueueSnackbar", "pushNotifications", "setPushNotifications", "browserPermission", "setBrowserPermission", "loading", "setLoading", "fcmSupported", "setFcmSupported", "then", "Notification", "permission", "pushNotification", "handlePushNotificationToggle", "event", "enabled", "target", "checked", "requestPermission", "variant", "vapid<PERSON>ey", "process", "env", "REACT_APP_FIREBASE_WEB_PUSH", "token", "USERS", "id", "fcmToken", "notificationUpdatedAt", "Date", "Error", "error", "console", "requestBrowserPermission", "children", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "sx", "mb", "color", "display", "alignItems", "gap", "size", "onClick", "justifyContent", "control", "onChange", "disabled", "label", "mt", "_c", "$RefreshReg$"], "sources": ["D:/Softwares/insyt-care/insyt-care-web/src/components/NotificationSettings.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  Typo<PERSON>,\n  Switch,\n  FormControlLabel,\n  Button,\n  Alert,\n  CircularProgress,\n  Divider\n} from '@mui/material';\nimport { useSelector } from 'react-redux';\nimport { doc, updateDoc } from 'firebase/firestore';\nimport { db, messaging } from '@config/firebase.config';\nimport { COLLECTIONS } from '@constants/app';\nimport { getToken, isSupported } from 'firebase/messaging';\nimport { useSnackbar } from 'notistack';\n\nconst NotificationSettings = () => {\n  const { user } = useSelector((state) => state.auth);\n  const { enqueueSnackbar } = useSnackbar();\n  \n  const [pushNotifications, setPushNotifications] = useState(true);\n  const [browserPermission, setBrowserPermission] = useState('default');\n  const [loading, setLoading] = useState(false);\n  const [fcmSupported, setFcmSupported] = useState(false);\n\n  useEffect(() => {\n    // Check if FCM is supported\n    isSupported().then(setFcmSupported);\n    \n    // Check browser notification permission\n    setBrowserPermission(Notification.permission);\n    \n    // Set initial state from user data\n    if (user) {\n      setPushNotifications(user.pushNotification !== false); // Default to true\n    }\n  }, [user]);\n\n  const handlePushNotificationToggle = async (event) => {\n    const enabled = event.target.checked;\n    setLoading(true);\n\n    try {\n      if (enabled) {\n        // If enabling notifications, request permission and get FCM token\n        if (Notification.permission !== 'granted') {\n          const permission = await Notification.requestPermission();\n          setBrowserPermission(permission);\n          \n          if (permission !== 'granted') {\n            enqueueSnackbar('Browser notification permission is required for push notifications.', { \n              variant: 'warning' \n            });\n            setLoading(false);\n            return;\n          }\n        }\n\n        // Get FCM token\n        if (fcmSupported) {\n          const vapidKey = process.env.REACT_APP_FIREBASE_WEB_PUSH;\n          if (!vapidKey) {\n            enqueueSnackbar('Push notification configuration is incomplete. Please contact administrator.', { \n              variant: 'error' \n            });\n            setLoading(false);\n            return;\n          }\n\n          const token = await getToken(messaging, { vapidKey });\n          if (token) {\n            // Update user document with FCM token and notification preference\n            await updateDoc(doc(db, COLLECTIONS.USERS, user.id), {\n              pushNotification: true,\n              fcmToken: token,\n              notificationUpdatedAt: new Date()\n            });\n            \n            setPushNotifications(true);\n            enqueueSnackbar('Push notifications enabled successfully!', { variant: 'success' });\n          } else {\n            throw new Error('Unable to get FCM token');\n          }\n        }\n      } else {\n        // If disabling notifications, just update the preference\n        await updateDoc(doc(db, COLLECTIONS.USERS, user.id), {\n          pushNotification: false,\n          notificationUpdatedAt: new Date()\n        });\n        \n        setPushNotifications(false);\n        enqueueSnackbar('Push notifications disabled.', { variant: 'info' });\n      }\n    } catch (error) {\n      console.error('Error updating notification settings:', error);\n      enqueueSnackbar('Failed to update notification settings. Please try again.', { \n        variant: 'error' \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const requestBrowserPermission = async () => {\n    try {\n      const permission = await Notification.requestPermission();\n      setBrowserPermission(permission);\n      \n      if (permission === 'granted') {\n        enqueueSnackbar('Browser notification permission granted!', { variant: 'success' });\n      } else {\n        enqueueSnackbar('Browser notification permission denied.', { variant: 'warning' });\n      }\n    } catch (error) {\n      console.error('Error requesting permission:', error);\n      enqueueSnackbar('Error requesting notification permission.', { variant: 'error' });\n    }\n  };\n\n  if (!fcmSupported) {\n    return (\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Notification Settings\n          </Typography>\n          <Alert severity=\"info\">\n            Push notifications are not supported in this browser.\n          </Alert>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom>\n          Notification Settings\n        </Typography>\n        \n        <Box sx={{ mb: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n            Manage your push notification preferences\n          </Typography>\n        </Box>\n\n        <Divider sx={{ mb: 2 }} />\n\n        {/* Browser Permission Status */}\n        <Box sx={{ mb: 2 }}>\n          <Typography variant=\"subtitle2\" gutterBottom>\n            Browser Permission Status\n          </Typography>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              {browserPermission === 'granted' && '✅ Granted'}\n              {browserPermission === 'denied' && '❌ Denied'}\n              {browserPermission === 'default' && '⏳ Not requested'}\n            </Typography>\n            {browserPermission !== 'granted' && (\n              <Button \n                size=\"small\" \n                variant=\"outlined\" \n                onClick={requestBrowserPermission}\n              >\n                Request Permission\n              </Button>\n            )}\n          </Box>\n        </Box>\n\n        <Divider sx={{ mb: 2 }} />\n\n        {/* Push Notification Toggle */}\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <Box>\n            <Typography variant=\"subtitle2\">\n              Push Notifications\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Receive notifications for new messages, appointments, and updates\n            </Typography>\n          </Box>\n          <FormControlLabel\n            control={\n              <Switch\n                checked={pushNotifications}\n                onChange={handlePushNotificationToggle}\n                disabled={loading || browserPermission === 'denied'}\n              />\n            }\n            label=\"\"\n          />\n        </Box>\n\n        {loading && (\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 2 }}>\n            <CircularProgress size={16} />\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Updating settings...\n            </Typography>\n          </Box>\n        )}\n\n        {browserPermission === 'denied' && (\n          <Alert severity=\"warning\" sx={{ mt: 2 }}>\n            Browser notifications are blocked. To enable push notifications, please allow notifications \n            in your browser settings and refresh the page.\n          </Alert>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default NotificationSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,QACF,eAAe;AACtB,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,GAAG,EAAEC,SAAS,QAAQ,oBAAoB;AACnD,SAASC,EAAE,EAAEC,SAAS,QAAQ,yBAAyB;AACvD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,EAAEC,WAAW,QAAQ,oBAAoB;AAC1D,SAASC,WAAW,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IAAEC;EAAK,CAAC,GAAGb,WAAW,CAAEc,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC;EACnD,MAAM;IAAEC;EAAgB,CAAC,GAAGR,WAAW,CAAC,CAAC;EAEzC,MAAM,CAACS,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC,SAAS,CAAC;EACrE,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd;IACAkB,WAAW,CAAC,CAAC,CAACkB,IAAI,CAACD,eAAe,CAAC;;IAEnC;IACAJ,oBAAoB,CAACM,YAAY,CAACC,UAAU,CAAC;;IAE7C;IACA,IAAId,IAAI,EAAE;MACRK,oBAAoB,CAACL,IAAI,CAACe,gBAAgB,KAAK,KAAK,CAAC,CAAC,CAAC;IACzD;EACF,CAAC,EAAE,CAACf,IAAI,CAAC,CAAC;EAEV,MAAMgB,4BAA4B,GAAG,MAAOC,KAAK,IAAK;IACpD,MAAMC,OAAO,GAAGD,KAAK,CAACE,MAAM,CAACC,OAAO;IACpCX,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,IAAIS,OAAO,EAAE;QACX;QACA,IAAIL,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;UACzC,MAAMA,UAAU,GAAG,MAAMD,YAAY,CAACQ,iBAAiB,CAAC,CAAC;UACzDd,oBAAoB,CAACO,UAAU,CAAC;UAEhC,IAAIA,UAAU,KAAK,SAAS,EAAE;YAC5BX,eAAe,CAAC,qEAAqE,EAAE;cACrFmB,OAAO,EAAE;YACX,CAAC,CAAC;YACFb,UAAU,CAAC,KAAK,CAAC;YACjB;UACF;QACF;;QAEA;QACA,IAAIC,YAAY,EAAE;UAChB,MAAMa,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,2BAA2B;UACxD,IAAI,CAACH,QAAQ,EAAE;YACbpB,eAAe,CAAC,8EAA8E,EAAE;cAC9FmB,OAAO,EAAE;YACX,CAAC,CAAC;YACFb,UAAU,CAAC,KAAK,CAAC;YACjB;UACF;UAEA,MAAMkB,KAAK,GAAG,MAAMlC,QAAQ,CAACF,SAAS,EAAE;YAAEgC;UAAS,CAAC,CAAC;UACrD,IAAII,KAAK,EAAE;YACT;YACA,MAAMtC,SAAS,CAACD,GAAG,CAACE,EAAE,EAAEE,WAAW,CAACoC,KAAK,EAAE5B,IAAI,CAAC6B,EAAE,CAAC,EAAE;cACnDd,gBAAgB,EAAE,IAAI;cACtBe,QAAQ,EAAEH,KAAK;cACfI,qBAAqB,EAAE,IAAIC,IAAI,CAAC;YAClC,CAAC,CAAC;YAEF3B,oBAAoB,CAAC,IAAI,CAAC;YAC1BF,eAAe,CAAC,0CAA0C,EAAE;cAAEmB,OAAO,EAAE;YAAU,CAAC,CAAC;UACrF,CAAC,MAAM;YACL,MAAM,IAAIW,KAAK,CAAC,yBAAyB,CAAC;UAC5C;QACF;MACF,CAAC,MAAM;QACL;QACA,MAAM5C,SAAS,CAACD,GAAG,CAACE,EAAE,EAAEE,WAAW,CAACoC,KAAK,EAAE5B,IAAI,CAAC6B,EAAE,CAAC,EAAE;UACnDd,gBAAgB,EAAE,KAAK;UACvBgB,qBAAqB,EAAE,IAAIC,IAAI,CAAC;QAClC,CAAC,CAAC;QAEF3B,oBAAoB,CAAC,KAAK,CAAC;QAC3BF,eAAe,CAAC,8BAA8B,EAAE;UAAEmB,OAAO,EAAE;QAAO,CAAC,CAAC;MACtE;IACF,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D/B,eAAe,CAAC,2DAA2D,EAAE;QAC3EmB,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,MAAMtB,UAAU,GAAG,MAAMD,YAAY,CAACQ,iBAAiB,CAAC,CAAC;MACzDd,oBAAoB,CAACO,UAAU,CAAC;MAEhC,IAAIA,UAAU,KAAK,SAAS,EAAE;QAC5BX,eAAe,CAAC,0CAA0C,EAAE;UAAEmB,OAAO,EAAE;QAAU,CAAC,CAAC;MACrF,CAAC,MAAM;QACLnB,eAAe,CAAC,yCAAyC,EAAE;UAAEmB,OAAO,EAAE;QAAU,CAAC,CAAC;MACpF;IACF,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD/B,eAAe,CAAC,2CAA2C,EAAE;QAAEmB,OAAO,EAAE;MAAQ,CAAC,CAAC;IACpF;EACF,CAAC;EAED,IAAI,CAACZ,YAAY,EAAE;IACjB,oBACEb,OAAA,CAACnB,IAAI;MAAA2D,QAAA,eACHxC,OAAA,CAAClB,WAAW;QAAA0D,QAAA,gBACVxC,OAAA,CAACjB,UAAU;UAAC0C,OAAO,EAAC,IAAI;UAACgB,YAAY;UAAAD,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb7C,OAAA,CAACb,KAAK;UAAC2D,QAAQ,EAAC,MAAM;UAAAN,QAAA,EAAC;QAEvB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEX;EAEA,oBACE7C,OAAA,CAACnB,IAAI;IAAA2D,QAAA,eACHxC,OAAA,CAAClB,WAAW;MAAA0D,QAAA,gBACVxC,OAAA,CAACjB,UAAU;QAAC0C,OAAO,EAAC,IAAI;QAACgB,YAAY;QAAAD,QAAA,EAAC;MAEtC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb7C,OAAA,CAACpB,GAAG;QAACmE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,eACjBxC,OAAA,CAACjB,UAAU;UAAC0C,OAAO,EAAC,OAAO;UAACwB,KAAK,EAAC,gBAAgB;UAACR,YAAY;UAAAD,QAAA,EAAC;QAEhE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEN7C,OAAA,CAACX,OAAO;QAAC0D,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG1B7C,OAAA,CAACpB,GAAG;QAACmE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACjBxC,OAAA,CAACjB,UAAU;UAAC0C,OAAO,EAAC,WAAW;UAACgB,YAAY;UAAAD,QAAA,EAAC;QAE7C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb7C,OAAA,CAACpB,GAAG;UAACmE,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAZ,QAAA,gBACzDxC,OAAA,CAACjB,UAAU;YAAC0C,OAAO,EAAC,OAAO;YAACwB,KAAK,EAAC,gBAAgB;YAAAT,QAAA,GAC/C/B,iBAAiB,KAAK,SAAS,IAAI,WAAW,EAC9CA,iBAAiB,KAAK,QAAQ,IAAI,UAAU,EAC5CA,iBAAiB,KAAK,SAAS,IAAI,iBAAiB;UAAA;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,EACZpC,iBAAiB,KAAK,SAAS,iBAC9BT,OAAA,CAACd,MAAM;YACLmE,IAAI,EAAC,OAAO;YACZ5B,OAAO,EAAC,UAAU;YAClB6B,OAAO,EAAEf,wBAAyB;YAAAC,QAAA,EACnC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7C,OAAA,CAACX,OAAO;QAAC0D,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG1B7C,OAAA,CAACpB,GAAG;QAACmE,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEI,cAAc,EAAE;QAAgB,CAAE;QAAAf,QAAA,gBAClFxC,OAAA,CAACpB,GAAG;UAAA4D,QAAA,gBACFxC,OAAA,CAACjB,UAAU;YAAC0C,OAAO,EAAC,WAAW;YAAAe,QAAA,EAAC;UAEhC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7C,OAAA,CAACjB,UAAU;YAAC0C,OAAO,EAAC,OAAO;YAACwB,KAAK,EAAC,gBAAgB;YAAAT,QAAA,EAAC;UAEnD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN7C,OAAA,CAACf,gBAAgB;UACfuE,OAAO,eACLxD,OAAA,CAAChB,MAAM;YACLuC,OAAO,EAAEhB,iBAAkB;YAC3BkD,QAAQ,EAAEtC,4BAA6B;YACvCuC,QAAQ,EAAE/C,OAAO,IAAIF,iBAAiB,KAAK;UAAS;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CACF;UACDc,KAAK,EAAC;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELlC,OAAO,iBACNX,OAAA,CAACpB,GAAG;QAACmE,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE,CAAC;UAAEQ,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,gBAChExC,OAAA,CAACZ,gBAAgB;UAACiE,IAAI,EAAE;QAAG;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9B7C,OAAA,CAACjB,UAAU;UAAC0C,OAAO,EAAC,OAAO;UAACwB,KAAK,EAAC,gBAAgB;UAAAT,QAAA,EAAC;QAEnD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN,EAEApC,iBAAiB,KAAK,QAAQ,iBAC7BT,OAAA,CAACb,KAAK;QAAC2D,QAAQ,EAAC,SAAS;QAACC,EAAE,EAAE;UAAEa,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,EAAC;MAGzC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAAC3C,EAAA,CAvMID,oBAAoB;EAAA,QACPX,WAAW,EACAQ,WAAW;AAAA;AAAA+D,EAAA,GAFnC5D,oBAAoB;AAyM1B,eAAeA,oBAAoB;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}