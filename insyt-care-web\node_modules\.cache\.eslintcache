[{"D:\\Softwares\\insyt-care\\insyt-care-web\\src\\index.jsx": "1", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\App.jsx": "2", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\store.js": "3", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\contexts\\interfaceContext.js": "4", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\contexts\\sidebarContext.js": "5", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\AppLayout.jsx": "6", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\global.js": "7", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\helpers.js": "8", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\mui-theme.js": "9", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\layout.js": "10", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\todos.js": "11", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\messenger.js": "12", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\cards.js": "13", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\auth.js": "14", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\appointments.js": "15", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\users.js": "16", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\chats.js": "17", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\tasks.js": "18", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\vars.js": "19", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layouts.js": "20", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\cards.js": "21", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\todos.js": "22", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\messenger.js": "23", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\app.js": "24", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Caregivers.jsx": "25", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Tasks.jsx": "26", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddCaregiver.jsx": "27", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddUser.jsx": "28", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Login.jsx": "29", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PageNotFound.jsx": "30", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Settings.jsx": "31", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Finances.jsx": "32", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DoctorsReviews.jsx": "33", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PatientMessenger.jsx": "34", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DoctorMessenger.jsx": "35", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Staff.jsx": "36", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Tests.jsx": "37", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AppointmentDetails.jsx": "38", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PatientReviews.jsx": "39", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DoctorAppointments.jsx": "40", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Appointments.jsx": "41", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardK.jsx": "42", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardJ.jsx": "43", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Nurses.jsx": "44", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardI.jsx": "45", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardH.jsx": "46", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardG.jsx": "47", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardF.jsx": "48", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardD.jsx": "49", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardE.jsx": "50", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardC.jsx": "51", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardB.jsx": "52", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardA.jsx": "53", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Dashboard.jsx": "54", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\config\\firebase.config.js": "55", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\ProtectedRoutes\\index.jsx": "56", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\PublicRoutes\\index.jsx": "57", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\storage.js": "58", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\options.js": "59", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useNotistack.js": "60", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\service\\firebase.service.js": "61", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UserSettings\\style.js": "62", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\style.js": "63", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\Content.jsx": "64", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\List.jsx": "65", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Page\\index.jsx": "66", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\index.jsx": "67", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\WidgetsLoader\\index.jsx": "68", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CaregiversList\\index.jsx": "69", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TaskPlanner\\index.jsx": "70", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\DropFiles\\index.jsx": "71", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UserSettings\\index.jsx": "72", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\WidgetBody\\index.jsx": "73", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Balance\\index.jsx": "74", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\index.jsx": "75", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Select\\index.jsx": "76", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\MaskedInputs\\Date\\index.jsx": "77", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TabNav\\index.jsx": "78", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\MaskedInputs\\Phone\\index.jsx": "79", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TabNav\\TabNavItem\\index.jsx": "80", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ModalWindow\\index.jsx": "81", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\index.jsx": "82", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\index.jsx": "83", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Field\\index.jsx": "84", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\LabeledFormInput\\index.jsx": "85", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Btn\\index.jsx": "86", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\UsersList\\index.jsx": "87", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\AppointmentListItem.jsx": "88", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsTests\\index.jsx": "89", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\StaffList\\index.jsx": "90", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\NoDataPlaceholder\\index.jsx": "91", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Avatar\\index.jsx": "92", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsList\\index.jsx": "93", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorCalendar\\index.jsx": "94", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientCalendar\\index.jsx": "95", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Statistics\\index.jsx": "96", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsHistory\\index.jsx": "97", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentTests\\index.jsx": "98", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\AppointmentsScheduler\\index.jsx": "99", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsOverview\\index.jsx": "100", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DiseaseRate\\index.jsx": "101", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsRadialBar\\index.jsx": "102", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RadarAreaChart\\index.jsx": "103", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NursesList\\index.jsx": "104", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyPlanner\\index.jsx": "105", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EpiContextPeriodChart\\index.jsx": "106", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecoveryRate\\index.jsx": "107", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\MapChart\\index.jsx": "108", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyAppointmentChart\\index.jsx": "109", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EpiContextAreaChart\\index.jsx": "110", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HealthIndexChart\\index.jsx": "111", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorRatingList\\index.jsx": "112", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PainLocation\\index.jsx": "113", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorCureRate\\index.jsx": "114", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HepatitisChart\\index.jsx": "115", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TaskScheduler\\index.jsx": "116", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsGenderLineChart\\index.jsx": "117", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\BloodTest\\index.jsx": "118", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HeartRate\\index.jsx": "119", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EventsCompactCalendar\\index.jsx": "120", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DiagnosesDonut\\index.jsx": "121", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyAppointmentsByDoc\\index.jsx": "122", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\usePageIsOverflow.js": "123", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\GenderScatter\\index.jsx": "124", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useWindowSize.js": "125", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentQuestions\\index.jsx": "126", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TasksList\\index.jsx": "127", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\ConfirmedDiagnoses\\index.jsx": "128", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientOverallAppointments\\index.jsx": "129", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientAppointmentsHistory\\index.jsx": "130", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NextPatient\\index.jsx": "131", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UpcomingAppointments\\index.jsx": "132", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\LaboratoryTests\\index.jsx": "133", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorOverallAppointment\\index.jsx": "134", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsPace\\index.jsx": "135", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\index.jsx": "136", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Panel\\index.jsx": "137", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ScrollProgress\\index.jsx": "138", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\doctors.js": "139", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\config\\axios.config.js": "140", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\style.js": "141", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\doctors_reviews.js": "142", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useContentHeight.js": "143", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\dates.js": "144", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\Review.jsx": "145", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Page\\style.js": "146", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useGenderFilter.js": "147", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useArrayNav.js": "148", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyPlanner\\style.js": "149", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Badge\\style.js": "150", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\GlobalSettingsControls\\style.js": "151", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TextArea\\TextArea.jsx": "152", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Navigator\\index.jsx": "153", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ScrollContainer\\index.jsx": "154", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\SelectPlaceholder\\index.jsx": "155", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\GlobalSettingsControls\\index.jsx": "156", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\CustomRating\\index.jsx": "157", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\DoctorRatingItem\\index.jsx": "158", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\WidgetHeader\\index.jsx": "159", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Grid\\index.jsx": "160", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CaregiversList\\Group\\index.jsx": "161", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\GenderNav\\index.jsx": "162", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\SearchBar\\index.jsx": "163", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\AssignShiftModal\\index.jsx": "164", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UserSettings\\Form\\index.jsx": "165", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PersonList\\Item\\index.jsx": "166", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\AddForm\\index.jsx": "167", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\TodosLegend\\index.jsx": "168", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\DnDLayout\\index.jsx": "169", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Balance\\style.js": "170", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\style.js": "171", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Select\\style.js": "172", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TabNav\\style.js": "173", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\style.js": "174", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\payments.js": "175", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\style.js": "176", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\MaskedInputs\\Date\\style.js": "177", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\style.js": "178", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsTests\\style.js": "179", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Card\\index.jsx": "180", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Form\\index.jsx": "181", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Main\\index.jsx": "182", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\UsersList\\User\\index.jsx": "183", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\Payments\\index.jsx": "184", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\patient_tests.js": "185", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\staff.js": "186", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\AppointmentChip.jsx": "187", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\AppointmentStatus.jsx": "188", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\framer.js": "189", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Statistics\\style.js": "190", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsList\\style.js": "191", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\payments_history.js": "192", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\colors.js": "193", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsOverview\\style.js": "194", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\AppointmentsScheduler\\style.js": "195", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\MapChart\\style.js": "196", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\usePeriodNav.js": "197", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\disease.js": "198", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\numbers.js": "199", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\epi_period.js": "200", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HealthIndexChart\\style.js": "201", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Reminder\\index.jsx": "202", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\index.jsx": "203", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ShapeButton\\index.jsx": "204", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\TestItem\\index.jsx": "205", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\GroupSeparator\\index.jsx": "206", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\health.js": "207", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\index.jsx": "208", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PainLocation\\style.js": "209", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\cure.js": "210", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TaskScheduler\\style.js": "211", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Navigator\\MonthNavigator\\index.jsx": "212", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\StaffList\\Group\\index.jsx": "213", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsList\\Group\\index.jsx": "214", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PaymentItem\\index.jsx": "215", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DailyNavigation\\index.jsx": "216", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\scheduler.js": "217", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\Event\\index.jsx": "218", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\SelectableSlot\\index.jsx": "219", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Legend\\index.jsx": "220", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\LabeledProgress\\index.jsx": "221", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PeriodNav\\index.jsx": "222", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HeartRate\\style.js": "223", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\gender.js": "224", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EventsCompactCalendar\\style.js": "225", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Legend\\LegendItem\\index.jsx": "226", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\scatter.js": "227", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentQuestions\\style.js": "228", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\confirmed.js": "229", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\appointments.js": "230", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\WidgetNav\\index.jsx": "231", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NursesList\\Group\\index.jsx": "232", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\CartesianChart\\index.jsx": "233", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Progress\\index.jsx": "234", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Timestamp\\index.jsx": "235", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PainLocation\\Body\\index.jsx": "236", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ModalWindow\\CloseBtn\\index.jsx": "237", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DiagnosesDonut\\ChartLabel\\index.jsx": "238", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Chart\\Tooltip\\index.jsx": "239", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Chart\\Labels\\index.jsx": "240", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\OverallAppointmentChart\\ChartLegend\\index.jsx": "241", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\OverallAppointmentChart\\ChartBars\\index.jsx": "242", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NextPatient\\style.js": "243", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\LaboratoryTests\\style.js": "244", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\upcoming.js": "245", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UpcomingAppointments\\style.js": "246", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentQuestions\\RecentQuestionsItem\\index.jsx": "247", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Truncated.jsx": "248", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\ConfirmedDiagnoses\\List\\index.jsx": "249", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentItem\\style.js": "250", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentItem\\index.jsx": "251", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\usePanelScroll.js": "252", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Panel\\style.js": "253", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\style.js": "254", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\MenuDots\\index.jsx": "255", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\IconLink\\index.jsx": "256", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Pill\\index.jsx": "257", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\Content\\index.jsx": "258", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\Emergency\\index.jsx": "259", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\MenuButton\\index.jsx": "260", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Logo\\index.jsx": "261", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Panel\\CurrentUser\\index.jsx": "262", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Navigator\\style.js": "263", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\DoctorRatingItem\\style.js": "264", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useElementScroll.js": "265", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\AssignShiftModal\\style.js": "266", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\RangeSlider\\index.jsx": "267", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PersonList\\index.jsx": "268", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\ChatBtn.jsx": "269", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PersonList\\Item\\style.js": "270", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\TextBtn.jsx": "271", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\EditBtn.jsx": "272", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\DnDLayout\\style.js": "273", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\AddForm\\style.js": "274", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\Todo\\index.jsx": "275", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ConfirmationModal\\index.jsx": "276", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\keyframes.js": "277", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Card\\style.js": "278", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Form\\style.js": "279", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\Payments\\style.js": "280", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\UsersList\\User\\style.js": "281", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\style.js": "282", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\TestItem\\style.js": "283", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\calendar_appointments.js": "284", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Radio\\index.jsx": "285", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Message\\index.jsx": "286", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Header\\index.jsx": "287", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Input\\index.jsx": "288", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\TimeSlot\\index.jsx": "289", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\WeeklyNavigation\\index.jsx": "290", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DailyToolbar\\index.jsx": "291", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\WeekSelector\\index.jsx": "292", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MonthlyNavigation\\index.jsx": "293", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MonthSelector\\index.jsx": "294", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DoctorPopup\\index.jsx": "295", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ScheduleAppointmentModal\\index.jsx": "296", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Legend\\style.js": "297", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\Event\\style.js": "298", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PaymentItem\\style.js": "299", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\recharts.js": "300", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\OverallAppointmentChart\\ChartBars\\style.js": "301", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\CustomTooltip\\index.jsx": "302", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\ConfirmedDiagnoses\\List\\style.js": "303", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\Emergency\\style.js": "304", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\menu.js": "305", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\Todo\\planner.js": "306", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\Todo\\list.js": "307", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Message\\style.js": "308", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Header\\style.js": "309", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Checkbox\\index.jsx": "310", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DoctorPopup\\style.js": "311", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Waveform\\index.jsx": "312", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\notifications.js": "313", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Notifications.jsx": "314", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatient.jsx": "315", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PatientDetails.jsx": "316", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Patients.jsx": "317", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep1.jsx": "318", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\NotificationItem\\index.jsx": "319", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep2.jsx": "320", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep4.jsx": "321", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep3.jsx": "322", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\CircularProgress\\index.jsx": "323", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentPatients\\index.jsx": "324", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TopStaff\\index.jsx": "325", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\StaffDetails.jsx": "326", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\appointmentValidation.js": "327", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentActionButtons\\index.jsx": "328", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useScrollLock.js": "329", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MobileAppointmentsList.jsx": "330", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\recurringAppointmentHelpers.js": "331", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MobileCalendar\\index.jsx": "332", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MobileCalendar\\style.js": "333", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\selectors\\chatSelectors.js": "334", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\phoneValidation.js": "335", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\PhoneNumberInput\\index.jsx": "336", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\VisitNotesSidebar\\index.jsx": "337", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\AddressInput\\index.jsx": "338", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AssignAdminModal\\index.jsx": "339", "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\NotificationSettings.jsx": "340"}, {"size": 493, "mtime": 1748930177455, "results": "341", "hashOfConfig": "342"}, {"size": 2423, "mtime": 1748930176893, "results": "343", "hashOfConfig": "342"}, {"size": 1042, "mtime": 1750422155861, "results": "344", "hashOfConfig": "342"}, {"size": 2878, "mtime": 1748930177406, "results": "345", "hashOfConfig": "342"}, {"size": 702, "mtime": 1748930177407, "results": "346", "hashOfConfig": "342"}, {"size": 7715, "mtime": 1753420754405, "results": "347", "hashOfConfig": "342"}, {"size": 9794, "mtime": 1750825468425, "results": "348", "hashOfConfig": "342"}, {"size": 2183, "mtime": 1748930177544, "results": "349", "hashOfConfig": "342"}, {"size": 1417, "mtime": 1752292100891, "results": "350", "hashOfConfig": "342"}, {"size": 805, "mtime": 1748930177530, "results": "351", "hashOfConfig": "342"}, {"size": 1721, "mtime": 1748930177533, "results": "352", "hashOfConfig": "342"}, {"size": 500, "mtime": 1748930177531, "results": "353", "hashOfConfig": "342"}, {"size": 1487, "mtime": 1748930177528, "results": "354", "hashOfConfig": "342"}, {"size": 1749, "mtime": 1748930177527, "results": "355", "hashOfConfig": "342"}, {"size": 6905, "mtime": 1751870641657, "results": "356", "hashOfConfig": "342"}, {"size": 11145, "mtime": 1753170691237, "results": "357", "hashOfConfig": "342"}, {"size": 7855, "mtime": 1751870641660, "results": "358", "hashOfConfig": "342"}, {"size": 4723, "mtime": 1749538416709, "results": "359", "hashOfConfig": "342"}, {"size": 2657, "mtime": 1751870641666, "results": "360", "hashOfConfig": "342"}, {"size": 9994, "mtime": 1749538416672, "results": "361", "hashOfConfig": "342"}, {"size": 928, "mtime": 1748930177411, "results": "362", "hashOfConfig": "342"}, {"size": 2363, "mtime": 1748930177433, "results": "363", "hashOfConfig": "342"}, {"size": 33263, "mtime": 1748930177424, "results": "364", "hashOfConfig": "342"}, {"size": 760, "mtime": 1751874477989, "results": "365", "hashOfConfig": "342"}, {"size": 2707, "mtime": 1748930177492, "results": "366", "hashOfConfig": "342"}, {"size": 448, "mtime": 1748930177521, "results": "367", "hashOfConfig": "342"}, {"size": 8122, "mtime": 1752292100868, "results": "368", "hashOfConfig": "342"}, {"size": 47118, "mtime": 1753150938914, "results": "369", "hashOfConfig": "342"}, {"size": 12922, "mtime": 1753420823543, "results": "370", "hashOfConfig": "342"}, {"size": 1560, "mtime": 1748930177515, "results": "371", "hashOfConfig": "342"}, {"size": 550, "mtime": 1753420921571, "results": "372", "hashOfConfig": "342"}, {"size": 581, "mtime": 1748930177512, "results": "373", "hashOfConfig": "342"}, {"size": 1690, "mtime": 1748930177511, "results": "374", "hashOfConfig": "342"}, {"size": 2387, "mtime": 1748930177517, "results": "375", "hashOfConfig": "342"}, {"size": 1657, "mtime": 1748930177510, "results": "376", "hashOfConfig": "342"}, {"size": 2527, "mtime": 1753175681225, "results": "377", "hashOfConfig": "342"}, {"size": 266, "mtime": 1748930177522, "results": "378", "hashOfConfig": "342"}, {"size": 17797, "mtime": 1752292100880, "results": "379", "hashOfConfig": "342"}, {"size": 1695, "mtime": 1748930177518, "results": "380", "hashOfConfig": "342"}, {"size": 796, "mtime": 1748930177509, "results": "381", "hashOfConfig": "342"}, {"size": 704, "mtime": 1749538416690, "results": "382", "hashOfConfig": "342"}, {"size": 1054, "mtime": 1748930177508, "results": "383", "hashOfConfig": "342"}, {"size": 945, "mtime": 1748930177506, "results": "384", "hashOfConfig": "342"}, {"size": 646, "mtime": 1748930177514, "results": "385", "hashOfConfig": "342"}, {"size": 820, "mtime": 1748930177505, "results": "386", "hashOfConfig": "342"}, {"size": 1011, "mtime": 1748930177504, "results": "387", "hashOfConfig": "342"}, {"size": 666, "mtime": 1748930177503, "results": "388", "hashOfConfig": "342"}, {"size": 818, "mtime": 1748930177502, "results": "389", "hashOfConfig": "342"}, {"size": 760, "mtime": 1748930177500, "results": "390", "hashOfConfig": "342"}, {"size": 782, "mtime": 1748930177501, "results": "391", "hashOfConfig": "342"}, {"size": 800, "mtime": 1748930177499, "results": "392", "hashOfConfig": "342"}, {"size": 811, "mtime": 1748930177497, "results": "393", "hashOfConfig": "342"}, {"size": 1407, "mtime": 1748930177497, "results": "394", "hashOfConfig": "342"}, {"size": 1753, "mtime": 1749708193025, "results": "395", "hashOfConfig": "342"}, {"size": 819, "mtime": 1749538416655, "results": "396", "hashOfConfig": "342"}, {"size": 1439, "mtime": 1748930177470, "results": "397", "hashOfConfig": "342"}, {"size": 669, "mtime": 1748930177472, "results": "398", "hashOfConfig": "342"}, {"size": 1732, "mtime": 1748930177548, "results": "399", "hashOfConfig": "342"}, {"size": 3504, "mtime": 1750422155813, "results": "400", "hashOfConfig": "342"}, {"size": 442, "mtime": 1748930177450, "results": "401", "hashOfConfig": "342"}, {"size": 733, "mtime": 1753170620798, "results": "402", "hashOfConfig": "342"}, {"size": 2203, "mtime": 1749538416743, "results": "403", "hashOfConfig": "342"}, {"size": 3239, "mtime": 1750858389295, "results": "404", "hashOfConfig": "342"}, {"size": 7587, "mtime": 1748930177361, "results": "405", "hashOfConfig": "342"}, {"size": 3079, "mtime": 1748930177362, "results": "406", "hashOfConfig": "342"}, {"size": 4360, "mtime": 1753160460319, "results": "407", "hashOfConfig": "342"}, {"size": 1000, "mtime": 1748930177393, "results": "408", "hashOfConfig": "342"}, {"size": 738, "mtime": 1748930177396, "results": "409", "hashOfConfig": "342"}, {"size": 3181, "mtime": 1749538416726, "results": "410", "hashOfConfig": "342"}, {"size": 3037, "mtime": 1749538416735, "results": "411", "hashOfConfig": "342"}, {"size": 1164, "mtime": 1748930177307, "results": "412", "hashOfConfig": "342"}, {"size": 1297, "mtime": 1748930177677, "results": "413", "hashOfConfig": "342"}, {"size": 535, "mtime": 1748930177389, "results": "414", "hashOfConfig": "342"}, {"size": 1140, "mtime": 1748930177553, "results": "415", "hashOfConfig": "342"}, {"size": 3636, "mtime": 1748930177571, "results": "416", "hashOfConfig": "342"}, {"size": 2082, "mtime": 1750422155679, "results": "417", "hashOfConfig": "342"}, {"size": 1945, "mtime": 1750830973898, "results": "418", "hashOfConfig": "342"}, {"size": 431, "mtime": 1748930176965, "results": "419", "hashOfConfig": "342"}, {"size": 537, "mtime": 1751874477986, "results": "420", "hashOfConfig": "342"}, {"size": 612, "mtime": 1748930176963, "results": "421", "hashOfConfig": "342"}, {"size": 2210, "mtime": 1751870641601, "results": "422", "hashOfConfig": "342"}, {"size": 680, "mtime": 1748930177336, "results": "423", "hashOfConfig": "342"}, {"size": 1781, "mtime": 1748930177644, "results": "424", "hashOfConfig": "342"}, {"size": 2211, "mtime": 1748930176919, "results": "425", "hashOfConfig": "342"}, {"size": 1049, "mtime": 1748930176925, "results": "426", "hashOfConfig": "342"}, {"size": 1922, "mtime": 1748930176907, "results": "427", "hashOfConfig": "342"}, {"size": 5942, "mtime": 1750858389295, "results": "428", "hashOfConfig": "342"}, {"size": 2513, "mtime": 1751870641548, "results": "429", "hashOfConfig": "342"}, {"size": 3236, "mtime": 1748930177638, "results": "430", "hashOfConfig": "342"}, {"size": 3128, "mtime": 1749538416734, "results": "431", "hashOfConfig": "342"}, {"size": 1756, "mtime": 1748930177344, "results": "432", "hashOfConfig": "342"}, {"size": 2169, "mtime": 1749538416604, "results": "433", "hashOfConfig": "342"}, {"size": 3719, "mtime": 1750422155883, "results": "434", "hashOfConfig": "342"}, {"size": 391, "mtime": 1748930177586, "results": "435", "hashOfConfig": "342"}, {"size": 386, "mtime": 1748930177626, "results": "436", "hashOfConfig": "342"}, {"size": 1440, "mtime": 1748930177662, "results": "437", "hashOfConfig": "342"}, {"size": 4206, "mtime": 1748930177646, "results": "438", "hashOfConfig": "342"}, {"size": 2640, "mtime": 1748930177656, "results": "439", "hashOfConfig": "342"}, {"size": 5797, "mtime": 1750758867143, "results": "440", "hashOfConfig": "342"}, {"size": 3297, "mtime": 1748930177648, "results": "441", "hashOfConfig": "342"}, {"size": 5418, "mtime": 1748930177584, "results": "442", "hashOfConfig": "342"}, {"size": 2907, "mtime": 1748930177636, "results": "443", "hashOfConfig": "342"}, {"size": 3242, "mtime": 1748930177651, "results": "444", "hashOfConfig": "342"}, {"size": 3459, "mtime": 1748930177619, "results": "445", "hashOfConfig": "342"}, {"size": 1955, "mtime": 1748930177578, "results": "446", "hashOfConfig": "342"}, {"size": 4039, "mtime": 1748930177596, "results": "447", "hashOfConfig": "342"}, {"size": 2936, "mtime": 1748930177658, "results": "448", "hashOfConfig": "342"}, {"size": 3773, "mtime": 1748930177613, "results": "449", "hashOfConfig": "342"}, {"size": 2366, "mtime": 1748930177574, "results": "450", "hashOfConfig": "342"}, {"size": 2064, "mtime": 1748930177593, "results": "451", "hashOfConfig": "342"}, {"size": 6581, "mtime": 1748930177602, "results": "452", "hashOfConfig": "342"}, {"size": 2484, "mtime": 1748930177591, "results": "453", "hashOfConfig": "342"}, {"size": 2026, "mtime": 1748930177622, "results": "454", "hashOfConfig": "342"}, {"size": 3334, "mtime": 1748930177588, "results": "455", "hashOfConfig": "342"}, {"size": 2161, "mtime": 1748930177609, "results": "456", "hashOfConfig": "342"}, {"size": 8065, "mtime": 1748930177667, "results": "457", "hashOfConfig": "342"}, {"size": 3457, "mtime": 1748930177629, "results": "458", "hashOfConfig": "342"}, {"size": 2007, "mtime": 1748930177556, "results": "459", "hashOfConfig": "342"}, {"size": 838, "mtime": 1748930177605, "results": "460", "hashOfConfig": "342"}, {"size": 3243, "mtime": 1748930177597, "results": "461", "hashOfConfig": "342"}, {"size": 4841, "mtime": 1748930177582, "results": "462", "hashOfConfig": "342"}, {"size": 4235, "mtime": 1748930177576, "results": "463", "hashOfConfig": "342"}, {"size": 661, "mtime": 1748930177452, "results": "464", "hashOfConfig": "342"}, {"size": 3749, "mtime": 1748930177601, "results": "465", "hashOfConfig": "342"}, {"size": 634, "mtime": 1748930177454, "results": "466", "hashOfConfig": "342"}, {"size": 4358, "mtime": 1750352348123, "results": "467", "hashOfConfig": "342"}, {"size": 2047, "mtime": 1748930177671, "results": "468", "hashOfConfig": "342"}, {"size": 1493, "mtime": 1748930177564, "results": "469", "hashOfConfig": "342"}, {"size": 2018, "mtime": 1749538416728, "results": "470", "hashOfConfig": "342"}, {"size": 2042, "mtime": 1748930177625, "results": "471", "hashOfConfig": "342"}, {"size": 4456, "mtime": 1748930177615, "results": "472", "hashOfConfig": "342"}, {"size": 5093, "mtime": 1750937862437, "results": "473", "hashOfConfig": "342"}, {"size": 3829, "mtime": 1748930177610, "results": "474", "hashOfConfig": "342"}, {"size": 2362, "mtime": 1748930177589, "results": "475", "hashOfConfig": "342"}, {"size": 4088, "mtime": 1748930177634, "results": "476", "hashOfConfig": "342"}, {"size": 2178, "mtime": 1751870641620, "results": "477", "hashOfConfig": "342"}, {"size": 4469, "mtime": 1751870641614, "results": "478", "hashOfConfig": "342"}, {"size": 1016, "mtime": 1748930176951, "results": "479", "hashOfConfig": "342"}, {"size": 5727, "mtime": 1748930177416, "results": "480", "hashOfConfig": "342"}, {"size": 1628, "mtime": 1753162253889, "results": "481", "hashOfConfig": "342"}, {"size": 3787, "mtime": 1748930177364, "results": "482", "hashOfConfig": "342"}, {"size": 73799, "mtime": 1748930177419, "results": "483", "hashOfConfig": "342"}, {"size": 551, "mtime": 1748930177447, "results": "484", "hashOfConfig": "342"}, {"size": 3727, "mtime": 1752292100894, "results": "485", "hashOfConfig": "342"}, {"size": 1543, "mtime": 1748930177363, "results": "486", "hashOfConfig": "342"}, {"size": 1255, "mtime": 1748930177464, "results": "487", "hashOfConfig": "342"}, {"size": 367, "mtime": 1748930177449, "results": "488", "hashOfConfig": "342"}, {"size": 562, "mtime": 1748930177446, "results": "489", "hashOfConfig": "342"}, {"size": 394, "mtime": 1748930177579, "results": "490", "hashOfConfig": "342"}, {"size": 1078, "mtime": 1748930176906, "results": "491", "hashOfConfig": "342"}, {"size": 1050, "mtime": 1748930177313, "results": "492", "hashOfConfig": "342"}, {"size": 1750, "mtime": 1749538416613, "results": "493", "hashOfConfig": "342"}, {"size": 958, "mtime": 1748930176940, "results": "494", "hashOfConfig": "342"}, {"size": 1439, "mtime": 1750858389295, "results": "495", "hashOfConfig": "342"}, {"size": 678, "mtime": 1748930176958, "results": "496", "hashOfConfig": "342"}, {"size": 3975, "mtime": 1748930177311, "results": "497", "hashOfConfig": "342"}, {"size": 941, "mtime": 1748930176914, "results": "498", "hashOfConfig": "342"}, {"size": 1120, "mtime": 1748930177304, "results": "499", "hashOfConfig": "342"}, {"size": 752, "mtime": 1748930177390, "results": "500", "hashOfConfig": "342"}, {"size": 1777, "mtime": 1750858389341, "results": "501", "hashOfConfig": "342"}, {"size": 890, "mtime": 1748930177558, "results": "502", "hashOfConfig": "342"}, {"size": 1125, "mtime": 1748930177309, "results": "503", "hashOfConfig": "342"}, {"size": 1917, "mtime": 1748930176953, "results": "504", "hashOfConfig": "342"}, {"size": 43071, "mtime": 1751870641557, "results": "505", "hashOfConfig": "342"}, {"size": 4488, "mtime": 1752292100901, "results": "506", "hashOfConfig": "342"}, {"size": 9175, "mtime": 1750422155735, "results": "507", "hashOfConfig": "342"}, {"size": 2965, "mtime": 1749538416653, "results": "508", "hashOfConfig": "342"}, {"size": 335, "mtime": 1748930177384, "results": "509", "hashOfConfig": "342"}, {"size": 2762, "mtime": 1750352366334, "results": "510", "hashOfConfig": "342"}, {"size": 836, "mtime": 1748930177554, "results": "511", "hashOfConfig": "342"}, {"size": 415, "mtime": 1748930177572, "results": "512", "hashOfConfig": "342"}, {"size": 3616, "mtime": 1750422155680, "results": "513", "hashOfConfig": "342"}, {"size": 1183, "mtime": 1748930176966, "results": "514", "hashOfConfig": "342"}, {"size": 1428, "mtime": 1750858389295, "results": "515", "hashOfConfig": "342"}, {"size": 2129, "mtime": 1748930177427, "results": "516", "hashOfConfig": "342"}, {"size": 715, "mtime": 1748930177645, "results": "517", "hashOfConfig": "342"}, {"size": 644, "mtime": 1750830827518, "results": "518", "hashOfConfig": "342"}, {"size": 3083, "mtime": 1748930177262, "results": "519", "hashOfConfig": "342"}, {"size": 881, "mtime": 1748930177639, "results": "520", "hashOfConfig": "342"}, {"size": 2934, "mtime": 1748930177566, "results": "521", "hashOfConfig": "342"}, {"size": 3108, "mtime": 1748930177569, "results": "522", "hashOfConfig": "342"}, {"size": 4272, "mtime": 1750858389356, "results": "523", "hashOfConfig": "342"}, {"size": 4992, "mtime": 1751870641593, "results": "524", "hashOfConfig": "342"}, {"size": 3417, "mtime": 1748930177642, "results": "525", "hashOfConfig": "342"}, {"size": 4796, "mtime": 1748930177425, "results": "526", "hashOfConfig": "342"}, {"size": 5166, "mtime": 1748930177432, "results": "527", "hashOfConfig": "342"}, {"size": 863, "mtime": 1748930177258, "results": "528", "hashOfConfig": "342"}, {"size": 1319, "mtime": 1751870641551, "results": "529", "hashOfConfig": "342"}, {"size": 161, "mtime": 1748930177402, "results": "530", "hashOfConfig": "342"}, {"size": 864, "mtime": 1748930177663, "results": "531", "hashOfConfig": "342"}, {"size": 1840, "mtime": 1748930177633, "results": "532", "hashOfConfig": "342"}, {"size": 2563, "mtime": 1748930177428, "results": "533", "hashOfConfig": "342"}, {"size": 526, "mtime": 1750828741460, "results": "534", "hashOfConfig": "342"}, {"size": 922, "mtime": 1748930177649, "results": "535", "hashOfConfig": "342"}, {"size": 2713, "mtime": 1750422155879, "results": "536", "hashOfConfig": "342"}, {"size": 717, "mtime": 1748930177613, "results": "537", "hashOfConfig": "342"}, {"size": 327, "mtime": 1748930177453, "results": "538", "hashOfConfig": "342"}, {"size": 1159, "mtime": 1748930177414, "results": "539", "hashOfConfig": "342"}, {"size": 341, "mtime": 1748930177545, "results": "540", "hashOfConfig": "342"}, {"size": 2738, "mtime": 1748930177420, "results": "541", "hashOfConfig": "342"}, {"size": 1400, "mtime": 1748930177604, "results": "542", "hashOfConfig": "342"}, {"size": 1079, "mtime": 1748930176950, "results": "543", "hashOfConfig": "342"}, {"size": 1392, "mtime": 1748930176901, "results": "544", "hashOfConfig": "342"}, {"size": 3065, "mtime": 1750649101227, "results": "545", "hashOfConfig": "342"}, {"size": 1074, "mtime": 1748930177370, "results": "546", "hashOfConfig": "342"}, {"size": 1215, "mtime": 1748930176921, "results": "547", "hashOfConfig": "342"}, {"size": 1707, "mtime": 1748930177422, "results": "548", "hashOfConfig": "342"}, {"size": 20594, "mtime": 1751870641581, "results": "549", "hashOfConfig": "342"}, {"size": 3715, "mtime": 1748930177623, "results": "550", "hashOfConfig": "342"}, {"size": 3715, "mtime": 1748930177413, "results": "551", "hashOfConfig": "342"}, {"size": 4296, "mtime": 1748930177668, "results": "552", "hashOfConfig": "342"}, {"size": 1763, "mtime": 1748930176939, "results": "553", "hashOfConfig": "342"}, {"size": 890, "mtime": 1748930177660, "results": "554", "hashOfConfig": "342"}, {"size": 739, "mtime": 1748930177631, "results": "555", "hashOfConfig": "342"}, {"size": 1143, "mtime": 1748930177351, "results": "556", "hashOfConfig": "342"}, {"size": 1281, "mtime": 1750422155693, "results": "557", "hashOfConfig": "342"}, {"size": 3094, "mtime": 1748930177431, "results": "558", "hashOfConfig": "342"}, {"size": 13468, "mtime": 1751870641567, "results": "559", "hashOfConfig": "342"}, {"size": 670, "mtime": 1750422155713, "results": "560", "hashOfConfig": "342"}, {"size": 388, "mtime": 1748930176930, "results": "561", "hashOfConfig": "342"}, {"size": 844, "mtime": 1748930176927, "results": "562", "hashOfConfig": "342"}, {"size": 635, "mtime": 1748930177354, "results": "563", "hashOfConfig": "342"}, {"size": 1810, "mtime": 1748930177607, "results": "564", "hashOfConfig": "342"}, {"size": 3915, "mtime": 1748930177421, "results": "565", "hashOfConfig": "342"}, {"size": 3764, "mtime": 1748930177599, "results": "566", "hashOfConfig": "342"}, {"size": 446, "mtime": 1748930176929, "results": "567", "hashOfConfig": "342"}, {"size": 1645, "mtime": 1748930177429, "results": "568", "hashOfConfig": "342"}, {"size": 757, "mtime": 1748930177655, "results": "569", "hashOfConfig": "342"}, {"size": 1715, "mtime": 1748930177412, "results": "570", "hashOfConfig": "342"}, {"size": 4695, "mtime": 1748930177408, "results": "571", "hashOfConfig": "342"}, {"size": 1048, "mtime": 1748930177392, "results": "572", "hashOfConfig": "342"}, {"size": 890, "mtime": 1748930177618, "results": "573", "hashOfConfig": "342"}, {"size": 6540, "mtime": 1748930177295, "results": "574", "hashOfConfig": "342"}, {"size": 1201, "mtime": 1748930176945, "results": "575", "hashOfConfig": "342"}, {"size": 646, "mtime": 1748930176969, "results": "576", "hashOfConfig": "342"}, {"size": 4282, "mtime": 1748930177621, "results": "577", "hashOfConfig": "342"}, {"size": 909, "mtime": 1750914251771, "results": "578", "hashOfConfig": "342"}, {"size": 1564, "mtime": 1748930177581, "results": "579", "hashOfConfig": "342"}, {"size": 1616, "mtime": 1748930177300, "results": "580", "hashOfConfig": "342"}, {"size": 276, "mtime": 1748930177298, "results": "581", "hashOfConfig": "342"}, {"size": 596, "mtime": 1748930177349, "results": "582", "hashOfConfig": "342"}, {"size": 2527, "mtime": 1748930177347, "results": "583", "hashOfConfig": "342"}, {"size": 217, "mtime": 1748930177616, "results": "584", "hashOfConfig": "342"}, {"size": 509, "mtime": 1748930177611, "results": "585", "hashOfConfig": "342"}, {"size": 6488, "mtime": 1748930177434, "results": "586", "hashOfConfig": "587"}, {"size": 1670, "mtime": 1748930177674, "results": "588", "hashOfConfig": "342"}, {"size": 1122, "mtime": 1748930177652, "results": "589", "hashOfConfig": "342"}, {"size": 612, "mtime": 1748930177385, "results": "590", "hashOfConfig": "342"}, {"size": 1588, "mtime": 1748930177562, "results": "591", "hashOfConfig": "342"}, {"size": 1387, "mtime": 1749554397518, "results": "592", "hashOfConfig": "342"}, {"size": 3542, "mtime": 1750422155687, "results": "593", "hashOfConfig": "342"}, {"size": 702, "mtime": 1748930177452, "results": "594", "hashOfConfig": "342"}, {"size": 4392, "mtime": 1750422155820, "results": "595", "hashOfConfig": "342"}, {"size": 1651, "mtime": 1751870641623, "results": "596", "hashOfConfig": "342"}, {"size": 773, "mtime": 1748930176937, "results": "597", "hashOfConfig": "342"}, {"size": 997, "mtime": 1748930176923, "results": "598", "hashOfConfig": "342"}, {"size": 1365, "mtime": 1748930176943, "results": "599", "hashOfConfig": "342"}, {"size": 3965, "mtime": 1751870641617, "results": "600", "hashOfConfig": "342"}, {"size": 531, "mtime": 1748930177476, "results": "601", "hashOfConfig": "342"}, {"size": 1128, "mtime": 1748930176935, "results": "602", "hashOfConfig": "342"}, {"size": 1266, "mtime": 1748930176933, "results": "603", "hashOfConfig": "342"}, {"size": 4334, "mtime": 1751870641609, "results": "604", "hashOfConfig": "342"}, {"size": 645, "mtime": 1748930176941, "results": "605", "hashOfConfig": "342"}, {"size": 1878, "mtime": 1748930177305, "results": "606", "hashOfConfig": "342"}, {"size": 1676, "mtime": 1748930177448, "results": "607", "hashOfConfig": "342"}, {"size": 3122, "mtime": 1751870641560, "results": "608", "hashOfConfig": "342"}, {"size": 801, "mtime": 1748930176948, "results": "609", "hashOfConfig": "342"}, {"size": 771, "mtime": 1749818406264, "results": "610", "hashOfConfig": "342"}, {"size": 1215, "mtime": 1748930176897, "results": "611", "hashOfConfig": "342"}, {"size": 3080, "mtime": 1749538416650, "results": "612", "hashOfConfig": "342"}, {"size": 1391, "mtime": 1749538416600, "results": "613", "hashOfConfig": "342"}, {"size": 1284, "mtime": 1749538416599, "results": "614", "hashOfConfig": "342"}, {"size": 904, "mtime": 1748930177378, "results": "615", "hashOfConfig": "342"}, {"size": 1058, "mtime": 1748930177375, "results": "616", "hashOfConfig": "342"}, {"size": 2633, "mtime": 1748930177380, "results": "617", "hashOfConfig": "342"}, {"size": 2276, "mtime": 1750422155725, "results": "618", "hashOfConfig": "342"}, {"size": 1235, "mtime": 1748930177538, "results": "619", "hashOfConfig": "342"}, {"size": 1144, "mtime": 1748930177567, "results": "620", "hashOfConfig": "342"}, {"size": 2169, "mtime": 1748930177570, "results": "621", "hashOfConfig": "342"}, {"size": 1508, "mtime": 1748930177643, "results": "622", "hashOfConfig": "342"}, {"size": 2388, "mtime": 1751870641597, "results": "623", "hashOfConfig": "342"}, {"size": 13174, "mtime": 1751870641585, "results": "624", "hashOfConfig": "342"}, {"size": 1486, "mtime": 1748930177371, "results": "625", "hashOfConfig": "342"}, {"size": 6977, "mtime": 1748930177410, "results": "626", "hashOfConfig": "342"}, {"size": 1186, "mtime": 1748930176946, "results": "627", "hashOfConfig": "342"}, {"size": 1152, "mtime": 1748930177328, "results": "628", "hashOfConfig": "342"}, {"size": 1242, "mtime": 1748930177321, "results": "629", "hashOfConfig": "342"}, {"size": 8075, "mtime": 1753364067887, "results": "630", "hashOfConfig": "342"}, {"size": 1418, "mtime": 1750758747675, "results": "631", "hashOfConfig": "342"}, {"size": 1113, "mtime": 1750422155719, "results": "632", "hashOfConfig": "342"}, {"size": 146, "mtime": 1748930177270, "results": "633", "hashOfConfig": "342"}, {"size": 788, "mtime": 1748930177287, "results": "634", "hashOfConfig": "342"}, {"size": 1394, "mtime": 1750422155711, "results": "635", "hashOfConfig": "342"}, {"size": 807, "mtime": 1748930177279, "results": "636", "hashOfConfig": "342"}, {"size": 9064, "mtime": 1750758922613, "results": "637", "hashOfConfig": "342"}, {"size": 21064, "mtime": 1751870641606, "results": "638", "hashOfConfig": "342"}, {"size": 456, "mtime": 1748930176931, "results": "639", "hashOfConfig": "342"}, {"size": 23470, "mtime": 1751870641572, "results": "640", "hashOfConfig": "342"}, {"size": 1137, "mtime": 1748930177352, "results": "641", "hashOfConfig": "342"}, {"size": 286, "mtime": 1748930177546, "results": "642", "hashOfConfig": "342"}, {"size": 1219, "mtime": 1748930177348, "results": "643", "hashOfConfig": "342"}, {"size": 1734, "mtime": 1748930176916, "results": "644", "hashOfConfig": "342"}, {"size": 482, "mtime": 1748930177563, "results": "645", "hashOfConfig": "342"}, {"size": 849, "mtime": 1748930177478, "results": "646", "hashOfConfig": "342"}, {"size": 3743, "mtime": 1750665092670, "results": "647", "hashOfConfig": "342"}, {"size": 687, "mtime": 1748930177382, "results": "648", "hashOfConfig": "342"}, {"size": 2312, "mtime": 1748930177381, "results": "649", "hashOfConfig": "342"}, {"size": 1343, "mtime": 1748930177330, "results": "650", "hashOfConfig": "342"}, {"size": 1028, "mtime": 1748930177322, "results": "651", "hashOfConfig": "342"}, {"size": 2129, "mtime": 1748930176909, "results": "652", "hashOfConfig": "342"}, {"size": 3059, "mtime": 1750914808372, "results": "653", "hashOfConfig": "342"}, {"size": 2941, "mtime": 1748930177386, "results": "654", "hashOfConfig": "342"}, {"size": 11535, "mtime": 1752292100888, "results": "655", "hashOfConfig": "342"}, {"size": 10067, "mtime": 1752292100884, "results": "656", "hashOfConfig": "342"}, {"size": 8082, "mtime": 1751874477992, "results": "657", "hashOfConfig": "342"}, {"size": 34682, "mtime": 1751870641646, "results": "658", "hashOfConfig": "342"}, {"size": 570, "mtime": 1749538416702, "results": "659", "hashOfConfig": "342"}, {"size": 29715, "mtime": 1753150842022, "results": "660", "hashOfConfig": "342"}, {"size": 2875, "mtime": 1751870641539, "results": "661", "hashOfConfig": "342"}, {"size": 20631, "mtime": 1749538416678, "results": "662", "hashOfConfig": "342"}, {"size": 21446, "mtime": 1750831400553, "results": "663", "hashOfConfig": "342"}, {"size": 18808, "mtime": 1749538416681, "results": "664", "hashOfConfig": "342"}, {"size": 1822, "mtime": 1749538416606, "results": "665", "hashOfConfig": "342"}, {"size": 9080, "mtime": 1750937924273, "results": "666", "hashOfConfig": "342"}, {"size": 5820, "mtime": 1750422155886, "results": "667", "hashOfConfig": "342"}, {"size": 18080, "mtime": 1751870641650, "results": "668", "hashOfConfig": "342"}, {"size": 24836, "mtime": 1751870641670, "results": "669", "hashOfConfig": "342"}, {"size": 9861, "mtime": 1752292100861, "results": "670", "hashOfConfig": "342"}, {"size": 1134, "mtime": 1749708193017, "results": "671", "hashOfConfig": "342"}, {"size": 5986, "mtime": 1751870641576, "results": "672", "hashOfConfig": "342"}, {"size": 5813, "mtime": 1751870641685, "results": "673", "hashOfConfig": "342"}, {"size": 4417, "mtime": 1750422155708, "results": "674", "hashOfConfig": "342"}, {"size": 4944, "mtime": 1750422155709, "results": "675", "hashOfConfig": "342"}, {"size": 492, "mtime": 1751870641653, "results": "676", "hashOfConfig": "342"}, {"size": 3378, "mtime": 1752292100897, "results": "677", "hashOfConfig": "342"}, {"size": 5091, "mtime": 1752292100846, "results": "678", "hashOfConfig": "342"}, {"size": 13313, "mtime": 1752292100864, "results": "679", "hashOfConfig": "342"}, {"size": 14214, "mtime": 1753150280763, "results": "680", "hashOfConfig": "342"}, {"size": 17514, "mtime": 1753175865074, "results": "681", "hashOfConfig": "342"}, {"size": 7210, "mtime": 1753420903895, "results": "682", "hashOfConfig": "342"}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xtwf30", {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1118", "messages": "1119", "suppressedMessages": "1120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1121", "messages": "1122", "suppressedMessages": "1123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1124", "messages": "1125", "suppressedMessages": "1126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1127", "messages": "1128", "suppressedMessages": "1129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1130", "messages": "1131", "suppressedMessages": "1132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1133", "messages": "1134", "suppressedMessages": "1135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1136", "messages": "1137", "suppressedMessages": "1138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1139", "messages": "1140", "suppressedMessages": "1141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1142", "messages": "1143", "suppressedMessages": "1144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1145", "messages": "1146", "suppressedMessages": "1147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1148", "messages": "1149", "suppressedMessages": "1150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1151", "messages": "1152", "suppressedMessages": "1153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1154", "messages": "1155", "suppressedMessages": "1156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1157", "messages": "1158", "suppressedMessages": "1159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1160", "messages": "1161", "suppressedMessages": "1162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1163", "messages": "1164", "suppressedMessages": "1165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1166", "messages": "1167", "suppressedMessages": "1168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1169", "messages": "1170", "suppressedMessages": "1171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1172", "messages": "1173", "suppressedMessages": "1174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1175", "messages": "1176", "suppressedMessages": "1177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1178", "messages": "1179", "suppressedMessages": "1180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1181", "messages": "1182", "suppressedMessages": "1183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1184", "messages": "1185", "suppressedMessages": "1186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1187", "messages": "1188", "suppressedMessages": "1189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1190", "messages": "1191", "suppressedMessages": "1192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1193", "messages": "1194", "suppressedMessages": "1195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1196", "messages": "1197", "suppressedMessages": "1198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1199", "messages": "1200", "suppressedMessages": "1201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1202", "messages": "1203", "suppressedMessages": "1204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1205", "messages": "1206", "suppressedMessages": "1207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1208", "messages": "1209", "suppressedMessages": "1210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1211", "messages": "1212", "suppressedMessages": "1213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1214", "messages": "1215", "suppressedMessages": "1216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1217", "messages": "1218", "suppressedMessages": "1219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1220", "messages": "1221", "suppressedMessages": "1222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1223", "messages": "1224", "suppressedMessages": "1225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1226", "messages": "1227", "suppressedMessages": "1228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1229", "messages": "1230", "suppressedMessages": "1231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1232", "messages": "1233", "suppressedMessages": "1234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1235", "messages": "1236", "suppressedMessages": "1237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1238", "messages": "1239", "suppressedMessages": "1240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1241", "messages": "1242", "suppressedMessages": "1243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1244", "messages": "1245", "suppressedMessages": "1246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1247", "messages": "1248", "suppressedMessages": "1249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1250", "messages": "1251", "suppressedMessages": "1252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1253", "messages": "1254", "suppressedMessages": "1255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1256", "messages": "1257", "suppressedMessages": "1258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1259", "messages": "1260", "suppressedMessages": "1261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1262", "messages": "1263", "suppressedMessages": "1264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1265", "messages": "1266", "suppressedMessages": "1267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1268", "messages": "1269", "suppressedMessages": "1270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1271", "messages": "1272", "suppressedMessages": "1273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1274", "messages": "1275", "suppressedMessages": "1276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1277", "messages": "1278", "suppressedMessages": "1279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1280", "messages": "1281", "suppressedMessages": "1282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1283", "messages": "1284", "suppressedMessages": "1285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1286", "messages": "1287", "suppressedMessages": "1288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1289", "messages": "1290", "suppressedMessages": "1291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1292", "messages": "1293", "suppressedMessages": "1294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1295", "messages": "1296", "suppressedMessages": "1297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1298", "messages": "1299", "suppressedMessages": "1300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1301", "messages": "1302", "suppressedMessages": "1303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1304", "messages": "1305", "suppressedMessages": "1306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1307", "messages": "1308", "suppressedMessages": "1309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1310", "messages": "1311", "suppressedMessages": "1312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1313", "messages": "1314", "suppressedMessages": "1315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1316", "messages": "1317", "suppressedMessages": "1318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1319", "messages": "1320", "suppressedMessages": "1321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1322", "messages": "1323", "suppressedMessages": "1324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1325", "messages": "1326", "suppressedMessages": "1327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1328", "messages": "1329", "suppressedMessages": "1330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1331", "messages": "1332", "suppressedMessages": "1333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1334", "messages": "1335", "suppressedMessages": "1336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1337", "messages": "1338", "suppressedMessages": "1339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1340", "messages": "1341", "suppressedMessages": "1342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1343", "messages": "1344", "suppressedMessages": "1345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1346", "messages": "1347", "suppressedMessages": "1348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1349", "messages": "1350", "suppressedMessages": "1351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1352", "messages": "1353", "suppressedMessages": "1354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1355", "messages": "1356", "suppressedMessages": "1357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1358", "messages": "1359", "suppressedMessages": "1360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1361", "messages": "1362", "suppressedMessages": "1363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1364", "messages": "1365", "suppressedMessages": "1366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1367", "messages": "1368", "suppressedMessages": "1369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1370", "messages": "1371", "suppressedMessages": "1372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1373", "messages": "1374", "suppressedMessages": "1375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1376", "messages": "1377", "suppressedMessages": "1378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1379", "messages": "1380", "suppressedMessages": "1381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1382", "messages": "1383", "suppressedMessages": "1384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1385", "messages": "1386", "suppressedMessages": "1387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1388", "messages": "1389", "suppressedMessages": "1390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1391", "messages": "1392", "suppressedMessages": "1393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1394", "messages": "1395", "suppressedMessages": "1396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1397", "messages": "1398", "suppressedMessages": "1399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1400", "messages": "1401", "suppressedMessages": "1402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1403", "messages": "1404", "suppressedMessages": "1405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1406", "messages": "1407", "suppressedMessages": "1408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1409", "messages": "1410", "suppressedMessages": "1411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1412", "messages": "1413", "suppressedMessages": "1414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1415", "messages": "1416", "suppressedMessages": "1417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gxv70p", {"filePath": "1418", "messages": "1419", "suppressedMessages": "1420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1421", "messages": "1422", "suppressedMessages": "1423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1424", "messages": "1425", "suppressedMessages": "1426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1427", "messages": "1428", "suppressedMessages": "1429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1430", "messages": "1431", "suppressedMessages": "1432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1433", "messages": "1434", "suppressedMessages": "1435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1436", "messages": "1437", "suppressedMessages": "1438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1439", "messages": "1440", "suppressedMessages": "1441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1442", "messages": "1443", "suppressedMessages": "1444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1445", "messages": "1446", "suppressedMessages": "1447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1448", "messages": "1449", "suppressedMessages": "1450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1451", "messages": "1452", "suppressedMessages": "1453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1454", "messages": "1455", "suppressedMessages": "1456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1457", "messages": "1458", "suppressedMessages": "1459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1460", "messages": "1461", "suppressedMessages": "1462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1463", "messages": "1464", "suppressedMessages": "1465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1466", "messages": "1467", "suppressedMessages": "1468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1469", "messages": "1470", "suppressedMessages": "1471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1472", "messages": "1473", "suppressedMessages": "1474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1475", "messages": "1476", "suppressedMessages": "1477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1478", "messages": "1479", "suppressedMessages": "1480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1481", "messages": "1482", "suppressedMessages": "1483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1484", "messages": "1485", "suppressedMessages": "1486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1487", "messages": "1488", "suppressedMessages": "1489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1490", "messages": "1491", "suppressedMessages": "1492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1493", "messages": "1494", "suppressedMessages": "1495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1496", "messages": "1497", "suppressedMessages": "1498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1499", "messages": "1500", "suppressedMessages": "1501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1502", "messages": "1503", "suppressedMessages": "1504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1505", "messages": "1506", "suppressedMessages": "1507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1508", "messages": "1509", "suppressedMessages": "1510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1511", "messages": "1512", "suppressedMessages": "1513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1514", "messages": "1515", "suppressedMessages": "1516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1517", "messages": "1518", "suppressedMessages": "1519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1520", "messages": "1521", "suppressedMessages": "1522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1523", "messages": "1524", "suppressedMessages": "1525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1526", "messages": "1527", "suppressedMessages": "1528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1529", "messages": "1530", "suppressedMessages": "1531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1532", "messages": "1533", "suppressedMessages": "1534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1535", "messages": "1536", "suppressedMessages": "1537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1538", "messages": "1539", "suppressedMessages": "1540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1541", "messages": "1542", "suppressedMessages": "1543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1544", "messages": "1545", "suppressedMessages": "1546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1547", "messages": "1548", "suppressedMessages": "1549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1550", "messages": "1551", "suppressedMessages": "1552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1553", "messages": "1554", "suppressedMessages": "1555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1556", "messages": "1557", "suppressedMessages": "1558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1559", "messages": "1560", "suppressedMessages": "1561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1562", "messages": "1563", "suppressedMessages": "1564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1565", "messages": "1566", "suppressedMessages": "1567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1568", "messages": "1569", "suppressedMessages": "1570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1571", "messages": "1572", "suppressedMessages": "1573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1574", "messages": "1575", "suppressedMessages": "1576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1577", "messages": "1578", "suppressedMessages": "1579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1580", "messages": "1581", "suppressedMessages": "1582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1583", "messages": "1584", "suppressedMessages": "1585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1586", "messages": "1587", "suppressedMessages": "1588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1589", "messages": "1590", "suppressedMessages": "1591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1592", "messages": "1593", "suppressedMessages": "1594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1595", "messages": "1596", "suppressedMessages": "1597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1598", "messages": "1599", "suppressedMessages": "1600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1601", "messages": "1602", "suppressedMessages": "1603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1604", "messages": "1605", "suppressedMessages": "1606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1607", "messages": "1608", "suppressedMessages": "1609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1610", "messages": "1611", "suppressedMessages": "1612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1613", "messages": "1614", "suppressedMessages": "1615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1616", "messages": "1617", "suppressedMessages": "1618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1619", "messages": "1620", "suppressedMessages": "1621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1622", "messages": "1623", "suppressedMessages": "1624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1625", "messages": "1626", "suppressedMessages": "1627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1628", "messages": "1629", "suppressedMessages": "1630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1631", "messages": "1632", "suppressedMessages": "1633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1634", "messages": "1635", "suppressedMessages": "1636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1637", "messages": "1638", "suppressedMessages": "1639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1640", "messages": "1641", "suppressedMessages": "1642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1643", "messages": "1644", "suppressedMessages": "1645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1646", "messages": "1647", "suppressedMessages": "1648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1649", "messages": "1650", "suppressedMessages": "1651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1652", "messages": "1653", "suppressedMessages": "1654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1655", "messages": "1656", "suppressedMessages": "1657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1658", "messages": "1659", "suppressedMessages": "1660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1661", "messages": "1662", "suppressedMessages": "1663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1664", "messages": "1665", "suppressedMessages": "1666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1667", "messages": "1668", "suppressedMessages": "1669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1670", "messages": "1671", "suppressedMessages": "1672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1673", "messages": "1674", "suppressedMessages": "1675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1676", "messages": "1677", "suppressedMessages": "1678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1679", "messages": "1680", "suppressedMessages": "1681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1682", "messages": "1683", "suppressedMessages": "1684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1685", "messages": "1686", "suppressedMessages": "1687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1688", "messages": "1689", "suppressedMessages": "1690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1691", "messages": "1692", "suppressedMessages": "1693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1694", "messages": "1695", "suppressedMessages": "1696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1697", "messages": "1698", "suppressedMessages": "1699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1700", "messages": "1701", "suppressedMessages": "1702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\App.jsx", [], ["1703", "1704"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\store.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\contexts\\interfaceContext.js", [], ["1705"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\contexts\\sidebarContext.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\AppLayout.jsx", ["1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\global.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\helpers.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\mui-theme.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\layout.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\todos.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\messenger.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\cards.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\auth.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\appointments.js", ["1729"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\users.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\chats.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\tasks.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\vars.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layouts.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\cards.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\todos.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\messenger.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\app.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Caregivers.jsx", ["1730", "1731"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Tasks.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddCaregiver.jsx", ["1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddUser.jsx", ["1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Login.jsx", ["1754", "1755", "1756"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PageNotFound.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Settings.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Finances.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DoctorsReviews.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PatientMessenger.jsx", ["1757"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DoctorMessenger.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Staff.jsx", ["1758"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Tests.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AppointmentDetails.jsx", ["1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PatientReviews.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DoctorAppointments.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Appointments.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardK.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardJ.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Nurses.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardI.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardH.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardG.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardF.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardD.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardE.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardC.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardB.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\DashboardA.jsx", ["1773", "1774", "1775"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Dashboard.jsx", ["1776", "1777", "1778", "1779", "1780", "1781", "1782"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\config\\firebase.config.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\ProtectedRoutes\\index.jsx", ["1783", "1784", "1785"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\PublicRoutes\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\storage.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\options.js", ["1786"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useNotistack.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\service\\firebase.service.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UserSettings\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\Content.jsx", [], ["1787", "1788"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\List.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Page\\index.jsx", ["1789", "1790", "1791", "1792", "1793"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\WidgetsLoader\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CaregiversList\\index.jsx", ["1794", "1795", "1796", "1797"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TaskPlanner\\index.jsx", ["1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\DropFiles\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UserSettings\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\WidgetBody\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Balance\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Select\\index.jsx", [], ["1815"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\MaskedInputs\\Date\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TabNav\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\MaskedInputs\\Phone\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TabNav\\TabNavItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ModalWindow\\index.jsx", ["1816"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\index.jsx", ["1817"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Field\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\LabeledFormInput\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Btn\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\UsersList\\index.jsx", ["1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\AppointmentListItem.jsx", ["1826", "1827", "1828", "1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsTests\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\StaffList\\index.jsx", ["1839", "1840"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\NoDataPlaceholder\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Avatar\\index.jsx", ["1841"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsList\\index.jsx", ["1842", "1843"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorCalendar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientCalendar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Statistics\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsHistory\\index.jsx", [], ["1844"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentTests\\index.jsx", [], ["1845"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\AppointmentsScheduler\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsOverview\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DiseaseRate\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsRadialBar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RadarAreaChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NursesList\\index.jsx", ["1846", "1847"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyPlanner\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EpiContextPeriodChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecoveryRate\\index.jsx", [], ["1848"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\MapChart\\index.jsx", [], ["1849"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyAppointmentChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EpiContextAreaChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HealthIndexChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorRatingList\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PainLocation\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorCureRate\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HepatitisChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TaskScheduler\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsGenderLineChart\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\BloodTest\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HeartRate\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EventsCompactCalendar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DiagnosesDonut\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyAppointmentsByDoc\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\usePageIsOverflow.js", [], ["1850"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\GenderScatter\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useWindowSize.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentQuestions\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TasksList\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\ConfirmedDiagnoses\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientOverallAppointments\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientAppointmentsHistory\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NextPatient\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UpcomingAppointments\\index.jsx", ["1851", "1852", "1853", "1854", "1855", "1856"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\LaboratoryTests\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DoctorOverallAppointment\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsPace\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\index.jsx", ["1857", "1858", "1859"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Panel\\index.jsx", ["1860", "1861", "1862", "1863", "1864", "1865", "1866", "1867"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ScrollProgress\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\doctors.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\config\\axios.config.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\doctors_reviews.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useContentHeight.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\dates.js", ["1868", "1869"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Reviews\\Review.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Page\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useGenderFilter.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useArrayNav.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DailyPlanner\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Badge\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\GlobalSettingsControls\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TextArea\\TextArea.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Navigator\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ScrollContainer\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\SelectPlaceholder\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\GlobalSettingsControls\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\CustomRating\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\DoctorRatingItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\WidgetHeader\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Grid\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CaregiversList\\Group\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\GenderNav\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\SearchBar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\AssignShiftModal\\index.jsx", ["1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UserSettings\\Form\\index.jsx", ["1890"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PersonList\\Item\\index.jsx", ["1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\AddForm\\index.jsx", ["1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912", "1913", "1914", "1915", "1916", "1917"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\TodosLegend\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\DnDLayout\\index.jsx", ["1918", "1919"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Balance\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Select\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\TabNav\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\payments.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\MaskedInputs\\Date\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsTests\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Card\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Form\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Main\\index.jsx", ["1920", "1921"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\UsersList\\User\\index.jsx", ["1922", "1923", "1924", "1925", "1926", "1927"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\Payments\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\patient_tests.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\staff.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\AppointmentChip.jsx", ["1928"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentListItem\\AppointmentStatus.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\framer.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\Statistics\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsList\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\payments_history.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\colors.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsOverview\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\AppointmentsScheduler\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\MapChart\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\usePeriodNav.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\disease.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\numbers.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\epi_period.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HealthIndexChart\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Reminder\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ShapeButton\\index.jsx", ["1929"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\TestItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\GroupSeparator\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\health.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\index.jsx", ["1930", "1931", "1932", "1933", "1934", "1935", "1936", "1937", "1938", "1939", "1940", "1941", "1942", "1943", "1944", "1945", "1946"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PainLocation\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\cure.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TaskScheduler\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Navigator\\MonthNavigator\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\StaffList\\Group\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PatientsList\\Group\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PaymentItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DailyNavigation\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\scheduler.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\Event\\index.jsx", ["1947", "1948", "1949", "1950", "1951", "1952"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\SelectableSlot\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Legend\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\LabeledProgress\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PeriodNav\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\HeartRate\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\gender.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\EventsCompactCalendar\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Legend\\LegendItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\scatter.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentQuestions\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\confirmed.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\appointments.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Widget\\WidgetNav\\index.jsx", ["1953"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NursesList\\Group\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\CartesianChart\\index.jsx", [], ["1954"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Progress\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Timestamp\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PainLocation\\Body\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ModalWindow\\CloseBtn\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\DiagnosesDonut\\ChartLabel\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Chart\\Tooltip\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Chart\\Labels\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\OverallAppointmentChart\\ChartLegend\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\OverallAppointmentChart\\ChartBars\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\NextPatient\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\LaboratoryTests\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\upcoming.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\UpcomingAppointments\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentQuestions\\RecentQuestionsItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Truncated.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\ConfirmedDiagnoses\\List\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentItem\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentItem\\index.jsx", ["1955", "1956"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\usePanelScroll.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Panel\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\MenuDots\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\IconLink\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Pill\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\Content\\index.jsx", ["1957"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\Emergency\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\MenuButton\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Logo\\index.jsx", ["1958", "1959"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Panel\\CurrentUser\\index.jsx", ["1960", "1961"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Navigator\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\DoctorRatingItem\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useElementScroll.js", [], ["1962"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\AssignShiftModal\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\RangeSlider\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PersonList\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\ChatBtn.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PersonList\\Item\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\TextBtn.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\ActionButton\\EditBtn.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\DnDLayout\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\AddForm\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\Todo\\index.jsx", ["1963", "1964", "1965", "1966", "1967"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ConfirmationModal\\index.jsx", ["1968", "1969", "1970", "1971"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\styles\\keyframes.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Card\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\CreditCards\\Form\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\PaymentsFeed\\Payments\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\UsersList\\User\\style.js", ["1972"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\TestItem\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\db\\calendar_appointments.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Radio\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Message\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Header\\index.jsx", ["1973", "1974", "1975"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Input\\index.jsx", ["1976", "1977", "1978", "1979", "1980", "1981", "1982"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\TimeSlot\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\WeeklyNavigation\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DailyToolbar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\WeekSelector\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MonthlyNavigation\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MonthSelector\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DoctorPopup\\index.jsx", ["1983", "1984", "1985", "1986", "1987", "1988", "1989", "1990", "1991", "1992"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\ScheduleAppointmentModal\\index.jsx", ["1993", "1994", "1995", "1996"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Legend\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\Event\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\PaymentItem\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\recharts.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\OverallAppointmentChart\\ChartBars\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\CustomTooltip\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\ConfirmedDiagnoses\\List\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\layout\\Sidebar\\Emergency\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\constants\\menu.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\Todo\\planner.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Todos\\Todo\\list.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Message\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Messenger\\Header\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\Checkbox\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\DoctorPopup\\style.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\Waveform\\index.jsx", [], ["1997"], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\slices\\notifications.js", ["1998", "1999"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Notifications.jsx", ["2000", "2001", "2002", "2003"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatient.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\PatientDetails.jsx", ["2004", "2005", "2006", "2007", "2008", "2009", "2010", "2011", "2012", "2013", "2014", "2015", "2016"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\Patients.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep1.jsx", ["2017", "2018", "2019", "2020"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\NotificationItem\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep2.jsx", ["2021", "2022", "2023"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep4.jsx", ["2024", "2025"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\AddPatientStep3.jsx", ["2026"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\CircularProgress\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\RecentPatients\\index.jsx", ["2027", "2028", "2029"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\widgets\\TopStaff\\index.jsx", ["2030", "2031"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\pages\\StaffDetails.jsx", ["2032", "2033", "2034", "2035", "2036", "2037", "2038", "2039", "2040", "2041", "2042", "2043"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\appointmentValidation.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentActionButtons\\index.jsx", ["2044", "2045", "2046", "2047", "2048", "2049", "2050"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\hooks\\useScrollLock.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MobileAppointmentsList.jsx", ["2051"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\recurringAppointmentHelpers.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MobileCalendar\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AppointmentsCalendar\\MobileCalendar\\style.js", ["2052"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\store\\selectors\\chatSelectors.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\utils\\phoneValidation.js", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\PhoneNumberInput\\index.jsx", [], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\VisitNotesSidebar\\index.jsx", ["2053", "2054", "2055", "2056", "2057", "2058"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\UI\\AddressInput\\index.jsx", ["2059", "2060", "2061"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\AssignAdminModal\\index.jsx", ["2062", "2063", "2064"], [], "D:\\Softwares\\insyt-care\\insyt-care-web\\src\\components\\NotificationSettings.jsx", [], [], {"ruleId": "2065", "severity": 1, "message": "2066", "line": 47, "column": 6, "nodeType": "2067", "endLine": 47, "endColumn": 17, "suggestions": "2068", "suppressions": "2069"}, {"ruleId": "2065", "severity": 1, "message": "2070", "line": 53, "column": 6, "nodeType": "2067", "endLine": 53, "endColumn": 8, "suggestions": "2071", "suppressions": "2072"}, {"ruleId": "2065", "severity": 1, "message": "2073", "line": 73, "column": 6, "nodeType": "2067", "endLine": 73, "endColumn": 81, "suggestions": "2074", "suppressions": "2075"}, {"ruleId": "2076", "severity": 1, "message": "2077", "line": 19, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 19, "endColumn": 33}, {"ruleId": "2076", "severity": 1, "message": "2080", "line": 19, "column": 35, "nodeType": "2078", "messageId": "2079", "endLine": 19, "endColumn": 58}, {"ruleId": "2076", "severity": 1, "message": "2081", "line": 26, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 26, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2082", "line": 27, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 27, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2083", "line": 28, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 28, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2084", "line": 29, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 29, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2085", "line": 30, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 30, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2086", "line": 31, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 31, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2087", "line": 32, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 32, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2088", "line": 33, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 33, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2089", "line": 34, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 34, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2090", "line": 35, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 35, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2091", "line": 36, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 36, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2092", "line": 37, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 37, "endColumn": 25}, {"ruleId": "2076", "severity": 1, "message": "2093", "line": 44, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 44, "endColumn": 12}, {"ruleId": "2076", "severity": 1, "message": "2094", "line": 48, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 48, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2095", "line": 49, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 49, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2096", "line": 50, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 50, "endColumn": 15}, {"ruleId": "2076", "severity": 1, "message": "2097", "line": 52, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 52, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2098", "line": 56, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 56, "endColumn": 19}, {"ruleId": "2065", "severity": 1, "message": "2099", "line": 76, "column": 6, "nodeType": "2067", "endLine": 76, "endColumn": 8, "suggestions": "2100"}, {"ruleId": "2065", "severity": 1, "message": "2099", "line": 100, "column": 6, "nodeType": "2067", "endLine": 100, "endColumn": 12, "suggestions": "2101"}, {"ruleId": "2065", "severity": 1, "message": "2102", "line": 152, "column": 6, "nodeType": "2067", "endLine": 152, "endColumn": 43, "suggestions": "2103"}, {"ruleId": "2076", "severity": 1, "message": "2104", "line": 3, "column": 27, "nodeType": "2078", "messageId": "2079", "endLine": 3, "endColumn": 33}, {"ruleId": "2076", "severity": 1, "message": "2105", "line": 11, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 11, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2106", "line": 19, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 19, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2107", "line": 12, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 12, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2108", "line": 13, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 13, "endColumn": 24}, {"ruleId": "2076", "severity": 1, "message": "2109", "line": 16, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 16, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2110", "line": 55, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 55, "endColumn": 18}, {"ruleId": "2076", "severity": 1, "message": "2111", "line": 59, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 59, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2112", "line": 76, "column": 5, "nodeType": "2078", "messageId": "2079", "endLine": 76, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2113", "line": 77, "column": 5, "nodeType": "2078", "messageId": "2079", "endLine": 77, "endColumn": 12}, {"ruleId": "2076", "severity": 1, "message": "2114", "line": 78, "column": 18, "nodeType": "2078", "messageId": "2079", "endLine": 78, "endColumn": 24}, {"ruleId": "2076", "severity": 1, "message": "2115", "line": 78, "column": 26, "nodeType": "2078", "messageId": "2079", "endLine": 78, "endColumn": 38}, {"ruleId": "2076", "severity": 1, "message": "2116", "line": 81, "column": 5, "nodeType": "2078", "messageId": "2079", "endLine": 81, "endColumn": 10}, {"ruleId": "2076", "severity": 1, "message": "2117", "line": 97, "column": 12, "nodeType": "2078", "messageId": "2079", "endLine": 97, "endColumn": 25}, {"ruleId": "2076", "severity": 1, "message": "2118", "line": 102, "column": 18, "nodeType": "2078", "messageId": "2079", "endLine": 102, "endColumn": 28}, {"ruleId": "2065", "severity": 1, "message": "2119", "line": 152, "column": 6, "nodeType": "2067", "endLine": 152, "endColumn": 26, "suggestions": "2120"}, {"ruleId": "2076", "severity": 1, "message": "2121", "line": 19, "column": 15, "nodeType": "2078", "messageId": "2079", "endLine": 19, "endColumn": 26}, {"ruleId": "2076", "severity": 1, "message": "2107", "line": 42, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 42, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2122", "line": 178, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 178, "endColumn": 17}, {"ruleId": "2065", "severity": 1, "message": "2123", "line": 568, "column": 6, "nodeType": "2067", "endLine": 568, "endColumn": 25, "suggestions": "2124"}, {"ruleId": "2065", "severity": 1, "message": "2125", "line": 577, "column": 6, "nodeType": "2067", "endLine": 577, "endColumn": 17, "suggestions": "2126"}, {"ruleId": "2127", "severity": 1, "message": "2128", "line": 745, "column": 38, "nodeType": "2129", "messageId": "2130", "endLine": 745, "endColumn": 40}, {"ruleId": "2131", "severity": 1, "message": "2132", "line": 904, "column": 31, "nodeType": "2133", "endLine": 904, "endColumn": 62}, {"ruleId": "2131", "severity": 1, "message": "2132", "line": 931, "column": 31, "nodeType": "2133", "endLine": 931, "endColumn": 61}, {"ruleId": "2131", "severity": 1, "message": "2132", "line": 1137, "column": 13, "nodeType": "2133", "endLine": 1140, "endColumn": 15}, {"ruleId": "2076", "severity": 1, "message": "2134", "line": 19, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 19, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2135", "line": 28, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 28, "endColumn": 22}, {"ruleId": "2065", "severity": 1, "message": "2136", "line": 178, "column": 6, "nodeType": "2067", "endLine": 178, "endColumn": 8, "suggestions": "2137"}, {"ruleId": "2065", "severity": 1, "message": "2138", "line": 54, "column": 6, "nodeType": "2067", "endLine": 54, "endColumn": 18, "suggestions": "2139"}, {"ruleId": "2076", "severity": 1, "message": "2140", "line": 10, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 10, "endColumn": 28}, {"ruleId": "2076", "severity": 1, "message": "2141", "line": 1, "column": 17, "nodeType": "2078", "messageId": "2079", "endLine": 1, "endColumn": 26}, {"ruleId": "2076", "severity": 1, "message": "2142", "line": 5, "column": 33, "nodeType": "2078", "messageId": "2079", "endLine": 5, "endColumn": 43}, {"ruleId": "2076", "severity": 1, "message": "2143", "line": 6, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 6, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2144", "line": 14, "column": 28, "nodeType": "2078", "messageId": "2079", "endLine": 14, "endColumn": 40}, {"ruleId": "2076", "severity": 1, "message": "2145", "line": 16, "column": 28, "nodeType": "2078", "messageId": "2079", "endLine": 16, "endColumn": 40}, {"ruleId": "2076", "severity": 1, "message": "2146", "line": 21, "column": 3, "nodeType": "2078", "messageId": "2079", "endLine": 21, "endColumn": 25}, {"ruleId": "2076", "severity": 1, "message": "2147", "line": 28, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 28, "endColumn": 12}, {"ruleId": "2076", "severity": 1, "message": "2148", "line": 29, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 29, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2149", "line": 30, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 30, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2150", "line": 139, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 139, "endColumn": 22}, {"ruleId": "2076", "severity": 1, "message": "2151", "line": 214, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 214, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2152", "line": 253, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 253, "endColumn": 24}, {"ruleId": "2076", "severity": 1, "message": "2153", "line": 257, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 257, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2154", "line": 281, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 281, "endColumn": 29}, {"ruleId": "2076", "severity": 1, "message": "2155", "line": 10, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 10, "endColumn": 29}, {"ruleId": "2076", "severity": 1, "message": "2156", "line": 11, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 11, "endColumn": 34}, {"ruleId": "2076", "severity": 1, "message": "2157", "line": 12, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 12, "endColumn": 25}, {"ruleId": "2076", "severity": 1, "message": "2158", "line": 3, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 3, "endColumn": 26}, {"ruleId": "2076", "severity": 1, "message": "2155", "line": 4, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 4, "endColumn": 29}, {"ruleId": "2076", "severity": 1, "message": "2159", "line": 5, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 5, "endColumn": 30}, {"ruleId": "2076", "severity": 1, "message": "2160", "line": 6, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 6, "endColumn": 22}, {"ruleId": "2076", "severity": 1, "message": "2161", "line": 7, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 7, "endColumn": 34}, {"ruleId": "2076", "severity": 1, "message": "2156", "line": 8, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 8, "endColumn": 34}, {"ruleId": "2076", "severity": 1, "message": "2162", "line": 9, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 9, "endColumn": 22}, {"ruleId": "2076", "severity": 1, "message": "2163", "line": 2, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 2, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2164", "line": 6, "column": 36, "nodeType": "2078", "messageId": "2079", "endLine": 6, "endColumn": 44}, {"ruleId": "2076", "severity": 1, "message": "2165", "line": 7, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 7, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2166", "line": 3, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 3, "endColumn": 16}, {"ruleId": "2065", "severity": 1, "message": "2167", "line": 75, "column": 8, "nodeType": "2067", "endLine": 75, "endColumn": 21, "suggestions": "2168", "suppressions": "2169"}, {"ruleId": "2065", "severity": 1, "message": "2170", "line": 80, "column": 8, "nodeType": "2067", "endLine": 80, "endColumn": 30, "suggestions": "2171", "suppressions": "2172"}, {"ruleId": "2076", "severity": 1, "message": "2173", "line": 9, "column": 3, "nodeType": "2078", "messageId": "2079", "endLine": 9, "endColumn": 18}, {"ruleId": "2076", "severity": 1, "message": "2174", "line": 11, "column": 3, "nodeType": "2078", "messageId": "2079", "endLine": 11, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2175", "line": 12, "column": 3, "nodeType": "2078", "messageId": "2079", "endLine": 12, "endColumn": 15}, {"ruleId": "2076", "severity": 1, "message": "2176", "line": 14, "column": 3, "nodeType": "2078", "messageId": "2079", "endLine": 14, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2177", "line": 39, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 39, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2178", "line": 11, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 11, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2179", "line": 18, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 18, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2180", "line": 26, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 26, "endColumn": 12}, {"ruleId": "2076", "severity": 1, "message": "2181", "line": 62, "column": 20, "nodeType": "2078", "messageId": "2079", "endLine": 62, "endColumn": 31}, {"ruleId": "2076", "severity": 1, "message": "2182", "line": 9, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 9, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2166", "line": 18, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 18, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2183", "line": 19, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 19, "endColumn": 11}, {"ruleId": "2076", "severity": 1, "message": "2184", "line": 20, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2185", "line": 20, "column": 15, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2186", "line": 20, "column": 23, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 32}, {"ruleId": "2076", "severity": 1, "message": "2187", "line": 21, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 21, "endColumn": 12}, {"ruleId": "2076", "severity": 1, "message": "2188", "line": 22, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 22, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2189", "line": 24, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 24, "endColumn": 29}, {"ruleId": "2076", "severity": 1, "message": "2190", "line": 27, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 27, "endColumn": 14}, {"ruleId": "2076", "severity": 1, "message": "2191", "line": 28, "column": 18, "nodeType": "2078", "messageId": "2079", "endLine": 28, "endColumn": 32}, {"ruleId": "2076", "severity": 1, "message": "2192", "line": 28, "column": 34, "nodeType": "2078", "messageId": "2079", "endLine": 28, "endColumn": 50}, {"ruleId": "2076", "severity": 1, "message": "2193", "line": 28, "column": 52, "nodeType": "2078", "messageId": "2079", "endLine": 28, "endColumn": 64}, {"ruleId": "2076", "severity": 1, "message": "2194", "line": 29, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 29, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2195", "line": 29, "column": 21, "nodeType": "2078", "messageId": "2079", "endLine": 29, "endColumn": 31}, {"ruleId": "2076", "severity": 1, "message": "2196", "line": 30, "column": 11, "nodeType": "2078", "messageId": "2079", "endLine": 30, "endColumn": 26}, {"ruleId": "2076", "severity": 1, "message": "2151", "line": 31, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 31, "endColumn": 17}, {"ruleId": "2065", "severity": 1, "message": "2197", "line": 27, "column": 6, "nodeType": "2067", "endLine": 27, "endColumn": 8, "suggestions": "2198", "suppressions": "2199"}, {"ruleId": "2076", "severity": 1, "message": "2141", "line": 10, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 10, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2200", "line": 4, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 4, "endColumn": 25}, {"ruleId": "2076", "severity": 1, "message": "2201", "line": 21, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 21, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2202", "line": 21, "column": 18, "nodeType": "2078", "messageId": "2079", "endLine": 21, "endColumn": 25}, {"ruleId": "2076", "severity": 1, "message": "2203", "line": 23, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 23, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2204", "line": 24, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 24, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2205", "line": 29, "column": 23, "nodeType": "2078", "messageId": "2079", "endLine": 29, "endColumn": 37}, {"ruleId": "2076", "severity": 1, "message": "2206", "line": 30, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 30, "endColumn": 23}, {"ruleId": "2076", "severity": 1, "message": "2207", "line": 45, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 45, "endColumn": 17}, {"ruleId": "2065", "severity": 1, "message": "2099", "line": 93, "column": 6, "nodeType": "2067", "endLine": 93, "endColumn": 26, "suggestions": "2208"}, {"ruleId": "2076", "severity": 1, "message": "2209", "line": 4, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 4, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2210", "line": 5, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 5, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2211", "line": 6, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 6, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2212", "line": 8, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 8, "endColumn": 14}, {"ruleId": "2076", "severity": 1, "message": "2213", "line": 9, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 9, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2214", "line": 9, "column": 18, "nodeType": "2078", "messageId": "2079", "endLine": 9, "endColumn": 22}, {"ruleId": "2076", "severity": 1, "message": "2215", "line": 9, "column": 24, "nodeType": "2078", "messageId": "2079", "endLine": 9, "endColumn": 28}, {"ruleId": "2076", "severity": 1, "message": "2216", "line": 9, "column": 30, "nodeType": "2078", "messageId": "2079", "endLine": 9, "endColumn": 35}, {"ruleId": "2076", "severity": 1, "message": "2217", "line": 9, "column": 37, "nodeType": "2078", "messageId": "2079", "endLine": 9, "endColumn": 42}, {"ruleId": "2076", "severity": 1, "message": "2218", "line": 9, "column": 44, "nodeType": "2078", "messageId": "2079", "endLine": 9, "endColumn": 53}, {"ruleId": "2076", "severity": 1, "message": "2219", "line": 10, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 10, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2220", "line": 11, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 11, "endColumn": 23}, {"ruleId": "2076", "severity": 1, "message": "2183", "line": 13, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 13, "endColumn": 11}, {"ruleId": "2076", "severity": 1, "message": "2221", "line": 25, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 25, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2180", "line": 27, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 27, "endColumn": 12}, {"ruleId": "2076", "severity": 1, "message": "2222", "line": 8, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 8, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2223", "line": 9, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 9, "endColumn": 22}, {"ruleId": "2076", "severity": 1, "message": "2224", "line": 31, "column": 17, "nodeType": "2078", "messageId": "2079", "endLine": 31, "endColumn": 25}, {"ruleId": "2065", "severity": 1, "message": "2225", "line": 66, "column": 8, "nodeType": "2067", "endLine": 66, "endColumn": 20, "suggestions": "2226", "suppressions": "2227"}, {"ruleId": "2065", "severity": 1, "message": "2225", "line": 45, "column": 8, "nodeType": "2067", "endLine": 45, "endColumn": 20, "suggestions": "2228", "suppressions": "2229"}, {"ruleId": "2076", "severity": 1, "message": "2221", "line": 25, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 25, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2180", "line": 27, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 27, "endColumn": 12}, {"ruleId": "2065", "severity": 1, "message": "2230", "line": 47, "column": 6, "nodeType": "2067", "endLine": 47, "endColumn": 8, "suggestions": "2231", "suppressions": "2232"}, {"ruleId": "2065", "severity": 1, "message": "2233", "line": 50, "column": 8, "nodeType": "2067", "endLine": 50, "endColumn": 15, "suggestions": "2234", "suppressions": "2235"}, {"ruleId": "2065", "severity": 1, "message": "2236", "line": 20, "column": 8, "nodeType": "2067", "endLine": 20, "endColumn": 10, "suggestions": "2237", "suppressions": "2238"}, {"ruleId": "2076", "severity": 1, "message": "2239", "line": 2, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 2, "endColumn": 28}, {"ruleId": "2076", "severity": 1, "message": "2240", "line": 9, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 9, "endColumn": 23}, {"ruleId": "2076", "severity": 1, "message": "2241", "line": 10, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 10, "endColumn": 30}, {"ruleId": "2076", "severity": 1, "message": "2242", "line": 11, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 11, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2141", "line": 19, "column": 18, "nodeType": "2078", "messageId": "2079", "endLine": 19, "endColumn": 27}, {"ruleId": "2076", "severity": 1, "message": "2151", "line": 26, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 26, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2243", "line": 12, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 12, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2244", "line": 13, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 13, "endColumn": 18}, {"ruleId": "2076", "severity": 1, "message": "2245", "line": 30, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 30, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2246", "line": 2, "column": 27, "nodeType": "2078", "messageId": "2079", "endLine": 2, "endColumn": 32}, {"ruleId": "2076", "severity": 1, "message": "2247", "line": 2, "column": 34, "nodeType": "2078", "messageId": "2079", "endLine": 2, "endColumn": 39}, {"ruleId": "2076", "severity": 1, "message": "2248", "line": 2, "column": 41, "nodeType": "2078", "messageId": "2079", "endLine": 2, "endColumn": 47}, {"ruleId": "2076", "severity": 1, "message": "2211", "line": 7, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 7, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2203", "line": 16, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 16, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2106", "line": 86, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 86, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2249", "line": 87, "column": 11, "nodeType": "2078", "messageId": "2079", "endLine": 87, "endColumn": 15}, {"ruleId": "2076", "severity": 1, "message": "2250", "line": 100, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 100, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2251", "line": 73, "column": 11, "nodeType": "2078", "messageId": "2079", "endLine": 73, "endColumn": 26}, {"ruleId": "2076", "severity": 1, "message": "2252", "line": 74, "column": 11, "nodeType": "2078", "messageId": "2079", "endLine": 74, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2246", "line": 3, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 3, "endColumn": 15}, {"ruleId": "2076", "severity": 1, "message": "2185", "line": 21, "column": 15, "nodeType": "2078", "messageId": "2079", "endLine": 21, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2253", "line": 21, "column": 23, "nodeType": "2078", "messageId": "2079", "endLine": 21, "endColumn": 32}, {"ruleId": "2076", "severity": 1, "message": "2166", "line": 24, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 24, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2254", "line": 157, "column": 15, "nodeType": "2078", "messageId": "2079", "endLine": 157, "endColumn": 28}, {"ruleId": "2076", "severity": 1, "message": "2255", "line": 158, "column": 15, "nodeType": "2078", "messageId": "2079", "endLine": 158, "endColumn": 26}, {"ruleId": "2076", "severity": 1, "message": "2256", "line": 204, "column": 5, "nodeType": "2078", "messageId": "2079", "endLine": 204, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2257", "line": 474, "column": 51, "nodeType": "2078", "messageId": "2079", "endLine": 474, "endColumn": 65}, {"ruleId": "2076", "severity": 1, "message": "2258", "line": 474, "column": 74, "nodeType": "2078", "messageId": "2079", "endLine": 474, "endColumn": 91}, {"ruleId": "2065", "severity": 1, "message": "2259", "line": 651, "column": 6, "nodeType": "2067", "endLine": 651, "endColumn": 12, "suggestions": "2260"}, {"ruleId": "2065", "severity": 1, "message": "2261", "line": 670, "column": 6, "nodeType": "2067", "endLine": 670, "endColumn": 30, "suggestions": "2262"}, {"ruleId": "2065", "severity": 1, "message": "2263", "line": 670, "column": 7, "nodeType": "2264", "endLine": 670, "endColumn": 29}, {"ruleId": "2065", "severity": 1, "message": "2261", "line": 677, "column": 6, "nodeType": "2067", "endLine": 677, "endColumn": 36, "suggestions": "2265"}, {"ruleId": "2065", "severity": 1, "message": "2263", "line": 677, "column": 7, "nodeType": "2264", "endLine": 677, "endColumn": 35}, {"ruleId": "2065", "severity": 1, "message": "2266", "line": 684, "column": 6, "nodeType": "2067", "endLine": 684, "endColumn": 12, "suggestions": "2267"}, {"ruleId": "2065", "severity": 1, "message": "2261", "line": 715, "column": 6, "nodeType": "2067", "endLine": 715, "endColumn": 94, "suggestions": "2268"}, {"ruleId": "2065", "severity": 1, "message": "2263", "line": 715, "column": 7, "nodeType": "2264", "endLine": 715, "endColumn": 25}, {"ruleId": "2065", "severity": 1, "message": "2263", "line": 715, "column": 27, "nodeType": "2264", "endLine": 715, "endColumn": 43}, {"ruleId": "2065", "severity": 1, "message": "2263", "line": 715, "column": 45, "nodeType": "2264", "endLine": 715, "endColumn": 63}, {"ruleId": "2065", "severity": 1, "message": "2263", "line": 715, "column": 65, "nodeType": "2264", "endLine": 715, "endColumn": 93}, {"ruleId": "2076", "severity": 1, "message": "2107", "line": 11, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 11, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2211", "line": 6, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 6, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2210", "line": 7, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 7, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2269", "line": 10, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 10, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2270", "line": 23, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 23, "endColumn": 15}, {"ruleId": "2076", "severity": 1, "message": "2271", "line": 24, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 24, "endColumn": 15}, {"ruleId": "2076", "severity": 1, "message": "2183", "line": 25, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 25, "endColumn": 11}, {"ruleId": "2076", "severity": 1, "message": "2272", "line": 27, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 27, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2273", "line": 27, "column": 44, "nodeType": "2078", "messageId": "2079", "endLine": 27, "endColumn": 53}, {"ruleId": "2076", "severity": 1, "message": "2274", "line": 138, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 138, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2275", "line": 2, "column": 28, "nodeType": "2078", "messageId": "2079", "endLine": 2, "endColumn": 40}, {"ruleId": "2076", "severity": 1, "message": "2178", "line": 5, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 5, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2276", "line": 6, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 6, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2277", "line": 13, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 13, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2278", "line": 13, "column": 19, "nodeType": "2078", "messageId": "2079", "endLine": 13, "endColumn": 33}, {"ruleId": "2076", "severity": 1, "message": "2279", "line": 14, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 14, "endColumn": 22}, {"ruleId": "2076", "severity": 1, "message": "2280", "line": 17, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 17, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2281", "line": 17, "column": 18, "nodeType": "2078", "messageId": "2079", "endLine": 17, "endColumn": 28}, {"ruleId": "2076", "severity": 1, "message": "2282", "line": 20, "column": 28, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 44}, {"ruleId": "2076", "severity": 1, "message": "2283", "line": 20, "column": 46, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 67}, {"ruleId": "2076", "severity": 1, "message": "2284", "line": 20, "column": 69, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 89}, {"ruleId": "2076", "severity": 1, "message": "2285", "line": 21, "column": 15, "nodeType": "2078", "messageId": "2079", "endLine": 21, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2142", "line": 21, "column": 23, "nodeType": "2078", "messageId": "2079", "endLine": 21, "endColumn": 33}, {"ruleId": "2076", "severity": 1, "message": "2286", "line": 22, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 22, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2193", "line": 26, "column": 11, "nodeType": "2078", "messageId": "2079", "endLine": 26, "endColumn": 23}, {"ruleId": "2076", "severity": 1, "message": "2192", "line": 26, "column": 25, "nodeType": "2078", "messageId": "2079", "endLine": 26, "endColumn": 41}, {"ruleId": "2076", "severity": 1, "message": "2191", "line": 26, "column": 43, "nodeType": "2078", "messageId": "2079", "endLine": 26, "endColumn": 57}, {"ruleId": "2076", "severity": 1, "message": "2287", "line": 26, "column": 59, "nodeType": "2078", "messageId": "2079", "endLine": 26, "endColumn": 71}, {"ruleId": "2076", "severity": 1, "message": "2165", "line": 12, "column": 23, "nodeType": "2078", "messageId": "2079", "endLine": 12, "endColumn": 34}, {"ruleId": "2076", "severity": 1, "message": "2288", "line": 20, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 15}, {"ruleId": "2076", "severity": 1, "message": "2289", "line": 20, "column": 22, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 29}, {"ruleId": "2065", "severity": 1, "message": "2290", "line": 69, "column": 6, "nodeType": "2067", "endLine": 69, "endColumn": 12, "suggestions": "2291"}, {"ruleId": "2076", "severity": 1, "message": "2244", "line": 6, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 6, "endColumn": 18}, {"ruleId": "2076", "severity": 1, "message": "2281", "line": 12, "column": 26, "nodeType": "2078", "messageId": "2079", "endLine": 12, "endColumn": 36}, {"ruleId": "2076", "severity": 1, "message": "2292", "line": 12, "column": 38, "nodeType": "2078", "messageId": "2079", "endLine": 12, "endColumn": 43}, {"ruleId": "2076", "severity": 1, "message": "2293", "line": 12, "column": 45, "nodeType": "2078", "messageId": "2079", "endLine": 12, "endColumn": 50}, {"ruleId": "2076", "severity": 1, "message": "2289", "line": 12, "column": 52, "nodeType": "2078", "messageId": "2079", "endLine": 12, "endColumn": 59}, {"ruleId": "2076", "severity": 1, "message": "2294", "line": 12, "column": 61, "nodeType": "2078", "messageId": "2079", "endLine": 12, "endColumn": 71}, {"ruleId": "2076", "severity": 1, "message": "2109", "line": 6, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 6, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2295", "line": 2, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 2, "endColumn": 15}, {"ruleId": "2076", "severity": 1, "message": "2296", "line": 20, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2297", "line": 28, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 28, "endColumn": 24}, {"ruleId": "2076", "severity": 1, "message": "2298", "line": 36, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 36, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2299", "line": 36, "column": 18, "nodeType": "2078", "messageId": "2079", "endLine": 36, "endColumn": 26}, {"ruleId": "2076", "severity": 1, "message": "2300", "line": 40, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 40, "endColumn": 32}, {"ruleId": "2076", "severity": 1, "message": "2106", "line": 48, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 48, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2301", "line": 52, "column": 11, "nodeType": "2078", "messageId": "2079", "endLine": 52, "endColumn": 21}, {"ruleId": "2065", "severity": 1, "message": "2302", "line": 119, "column": 6, "nodeType": "2067", "endLine": 119, "endColumn": 29, "suggestions": "2303"}, {"ruleId": "2065", "severity": 1, "message": "2304", "line": 129, "column": 6, "nodeType": "2067", "endLine": 129, "endColumn": 23, "suggestions": "2305"}, {"ruleId": "2076", "severity": 1, "message": "2306", "line": 228, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 228, "endColumn": 24}, {"ruleId": "2076", "severity": 1, "message": "2307", "line": 228, "column": 26, "nodeType": "2078", "messageId": "2079", "endLine": 228, "endColumn": 41}, {"ruleId": "2076", "severity": 1, "message": "2308", "line": 367, "column": 13, "nodeType": "2078", "messageId": "2079", "endLine": 367, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2308", "line": 380, "column": 13, "nodeType": "2078", "messageId": "2079", "endLine": 380, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2309", "line": 381, "column": 13, "nodeType": "2078", "messageId": "2079", "endLine": 381, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2308", "line": 398, "column": 13, "nodeType": "2078", "messageId": "2079", "endLine": 398, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2309", "line": 399, "column": 13, "nodeType": "2078", "messageId": "2079", "endLine": 399, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2308", "line": 414, "column": 11, "nodeType": "2078", "messageId": "2079", "endLine": 414, "endColumn": 14}, {"ruleId": "2076", "severity": 1, "message": "2310", "line": 15, "column": 20, "nodeType": "2078", "messageId": "2079", "endLine": 15, "endColumn": 26}, {"ruleId": "2076", "severity": 1, "message": "2141", "line": 15, "column": 28, "nodeType": "2078", "messageId": "2079", "endLine": 15, "endColumn": 37}, {"ruleId": "2076", "severity": 1, "message": "2311", "line": 21, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 21, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2312", "line": 44, "column": 13, "nodeType": "2078", "messageId": "2079", "endLine": 44, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2313", "line": 45, "column": 13, "nodeType": "2078", "messageId": "2079", "endLine": 45, "endColumn": 22}, {"ruleId": "2314", "severity": 1, "message": "2315", "line": 73, "column": 7, "nodeType": "2316", "messageId": "2130", "endLine": 73, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2317", "line": 2, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 2, "endColumn": 13}, {"ruleId": "2065", "severity": 1, "message": "2318", "line": 26, "column": 8, "nodeType": "2067", "endLine": 26, "endColumn": 15, "suggestions": "2319", "suppressions": "2320"}, {"ruleId": "2076", "severity": 1, "message": "2211", "line": 7, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 7, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2321", "line": 22, "column": 33, "nodeType": "2078", "messageId": "2079", "endLine": 22, "endColumn": 39}, {"ruleId": "2076", "severity": 1, "message": "2322", "line": 15, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 15, "endColumn": 14}, {"ruleId": "2076", "severity": 1, "message": "2323", "line": 15, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 15, "endColumn": 10}, {"ruleId": "2076", "severity": 1, "message": "2324", "line": 35, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 35, "endColumn": 11}, {"ruleId": "2076", "severity": 1, "message": "2325", "line": 14, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 14, "endColumn": 15}, {"ruleId": "2076", "severity": 1, "message": "2326", "line": 15, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 15, "endColumn": 16}, {"ruleId": "2065", "severity": 1, "message": "2327", "line": 40, "column": 9, "nodeType": "2067", "endLine": 40, "endColumn": 39, "suggestions": "2328", "suppressions": "2329"}, {"ruleId": "2076", "severity": 1, "message": "2330", "line": 19, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 19, "endColumn": 26}, {"ruleId": "2076", "severity": 1, "message": "2184", "line": 20, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2186", "line": 20, "column": 15, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 24}, {"ruleId": "2076", "severity": 1, "message": "2187", "line": 21, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 21, "endColumn": 12}, {"ruleId": "2076", "severity": 1, "message": "2188", "line": 22, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 22, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2331", "line": 4, "column": 3, "nodeType": "2078", "messageId": "2079", "endLine": 4, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2332", "line": 5, "column": 3, "nodeType": "2078", "messageId": "2079", "endLine": 5, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2333", "line": 6, "column": 3, "nodeType": "2078", "messageId": "2079", "endLine": 6, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2183", "line": 13, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 13, "endColumn": 11}, {"ruleId": "2076", "severity": 1, "message": "2334", "line": 5, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 5, "endColumn": 12}, {"ruleId": "2076", "severity": 1, "message": "2211", "line": 6, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 6, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2210", "line": 7, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 7, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2335", "line": 14, "column": 46, "nodeType": "2078", "messageId": "2079", "endLine": 14, "endColumn": 52}, {"ruleId": "2076", "severity": 1, "message": "2211", "line": 7, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 7, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2336", "line": 15, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 15, "endColumn": 14}, {"ruleId": "2076", "severity": 1, "message": "2337", "line": 20, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2338", "line": 23, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 23, "endColumn": 26}, {"ruleId": "2076", "severity": 1, "message": "2183", "line": 24, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 24, "endColumn": 11}, {"ruleId": "2076", "severity": 1, "message": "2339", "line": 26, "column": 27, "nodeType": "2078", "messageId": "2079", "endLine": 26, "endColumn": 34}, {"ruleId": "2076", "severity": 1, "message": "2151", "line": 110, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 110, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2340", "line": 15, "column": 63, "nodeType": "2078", "messageId": "2079", "endLine": 15, "endColumn": 72}, {"ruleId": "2076", "severity": 1, "message": "2341", "line": 16, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 16, "endColumn": 23}, {"ruleId": "2076", "severity": 1, "message": "2342", "line": 16, "column": 25, "nodeType": "2078", "messageId": "2079", "endLine": 16, "endColumn": 35}, {"ruleId": "2076", "severity": 1, "message": "2343", "line": 80, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 80, "endColumn": 25}, {"ruleId": "2076", "severity": 1, "message": "2344", "line": 81, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 81, "endColumn": 22}, {"ruleId": "2076", "severity": 1, "message": "2345", "line": 82, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 82, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2346", "line": 82, "column": 18, "nodeType": "2078", "messageId": "2079", "endLine": 82, "endColumn": 27}, {"ruleId": "2076", "severity": 1, "message": "2347", "line": 84, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 84, "endColumn": 28}, {"ruleId": "2076", "severity": 1, "message": "2348", "line": 94, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 94, "endColumn": 28}, {"ruleId": "2076", "severity": 1, "message": "2349", "line": 97, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 97, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2350", "line": 3, "column": 36, "nodeType": "2078", "messageId": "2079", "endLine": 3, "endColumn": 43}, {"ruleId": "2076", "severity": 1, "message": "2165", "line": 20, "column": 23, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 34}, {"ruleId": "2076", "severity": 1, "message": "2110", "line": 45, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 45, "endColumn": 18}, {"ruleId": "2065", "severity": 1, "message": "2351", "line": 313, "column": 6, "nodeType": "2067", "endLine": 313, "endColumn": 17, "suggestions": "2352"}, {"ruleId": "2065", "severity": 1, "message": "2353", "line": 78, "column": 6, "nodeType": "2067", "endLine": 78, "endColumn": 8, "suggestions": "2354", "suppressions": "2355"}, {"ruleId": "2076", "severity": 1, "message": "2184", "line": 3, "column": 22, "nodeType": "2078", "messageId": "2079", "endLine": 3, "endColumn": 25}, {"ruleId": "2076", "severity": 1, "message": "2185", "line": 3, "column": 52, "nodeType": "2078", "messageId": "2079", "endLine": 3, "endColumn": 58}, {"ruleId": "2065", "severity": 1, "message": "2356", "line": 108, "column": 6, "nodeType": "2067", "endLine": 108, "endColumn": 17, "suggestions": "2357"}, {"ruleId": "2076", "severity": 1, "message": "2116", "line": 114, "column": 5, "nodeType": "2078", "messageId": "2079", "endLine": 114, "endColumn": 10}, {"ruleId": "2076", "severity": 1, "message": "2358", "line": 115, "column": 5, "nodeType": "2078", "messageId": "2079", "endLine": 115, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2359", "line": 165, "column": 13, "nodeType": "2078", "messageId": "2079", "endLine": 165, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2360", "line": 4, "column": 15, "nodeType": "2078", "messageId": "2079", "endLine": 4, "endColumn": 31}, {"ruleId": "2076", "severity": 1, "message": "2361", "line": 5, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 5, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2362", "line": 18, "column": 28, "nodeType": "2078", "messageId": "2079", "endLine": 18, "endColumn": 51}, {"ruleId": "2076", "severity": 1, "message": "2363", "line": 22, "column": 15, "nodeType": "2078", "messageId": "2079", "endLine": 22, "endColumn": 27}, {"ruleId": "2076", "severity": 1, "message": "2364", "line": 23, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 23, "endColumn": 14}, {"ruleId": "2076", "severity": 1, "message": "2365", "line": 31, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 31, "endColumn": 12}, {"ruleId": "2076", "severity": 1, "message": "2366", "line": 88, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 88, "endColumn": 22}, {"ruleId": "2076", "severity": 1, "message": "2367", "line": 95, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 95, "endColumn": 18}, {"ruleId": "2076", "severity": 1, "message": "2368", "line": 167, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 167, "endColumn": 18}, {"ruleId": "2076", "severity": 1, "message": "2369", "line": 168, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 168, "endColumn": 14}, {"ruleId": "2076", "severity": 1, "message": "2370", "line": 169, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 169, "endColumn": 28}, {"ruleId": "2076", "severity": 1, "message": "2371", "line": 170, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 170, "endColumn": 22}, {"ruleId": "2076", "severity": 1, "message": "2372", "line": 180, "column": 12, "nodeType": "2078", "messageId": "2079", "endLine": 180, "endColumn": 24}, {"ruleId": "2076", "severity": 1, "message": "2107", "line": 27, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 27, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2373", "line": 165, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 165, "endColumn": 18}, {"ruleId": "2065", "severity": 1, "message": "2259", "line": 421, "column": 6, "nodeType": "2067", "endLine": 421, "endColumn": 22, "suggestions": "2374"}, {"ruleId": "2131", "severity": 1, "message": "2132", "line": 768, "column": 23, "nodeType": "2133", "endLine": 768, "endColumn": 41}, {"ruleId": "2076", "severity": 1, "message": "2375", "line": 78, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 78, "endColumn": 22}, {"ruleId": "2076", "severity": 1, "message": "2376", "line": 78, "column": 24, "nodeType": "2078", "messageId": "2079", "endLine": 78, "endColumn": 39}, {"ruleId": "2065", "severity": 1, "message": "2259", "line": 222, "column": 6, "nodeType": "2067", "endLine": 222, "endColumn": 22, "suggestions": "2377"}, {"ruleId": "2076", "severity": 1, "message": "2122", "line": 37, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 37, "endColumn": 17}, {"ruleId": "2065", "severity": 1, "message": "2378", "line": 302, "column": 6, "nodeType": "2067", "endLine": 302, "endColumn": 8, "suggestions": "2379"}, {"ruleId": "2065", "severity": 1, "message": "2259", "line": 156, "column": 6, "nodeType": "2067", "endLine": 156, "endColumn": 22, "suggestions": "2380"}, {"ruleId": "2076", "severity": 1, "message": "2141", "line": 1, "column": 18, "nodeType": "2078", "messageId": "2079", "endLine": 1, "endColumn": 27}, {"ruleId": "2076", "severity": 1, "message": "2151", "line": 20, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 20, "endColumn": 17}, {"ruleId": "2065", "severity": 1, "message": "2381", "line": 28, "column": 9, "nodeType": "2382", "endLine": 60, "endColumn": 13}, {"ruleId": "2076", "severity": 1, "message": "2213", "line": 12, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 12, "endColumn": 16}, {"ruleId": "2076", "severity": 1, "message": "2383", "line": 39, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 39, "endColumn": 18}, {"ruleId": "2076", "severity": 1, "message": "2384", "line": 18, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 18, "endColumn": 27}, {"ruleId": "2076", "severity": 1, "message": "2122", "line": 87, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 87, "endColumn": 17}, {"ruleId": "2076", "severity": 1, "message": "2368", "line": 139, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 139, "endColumn": 18}, {"ruleId": "2076", "severity": 1, "message": "2369", "line": 140, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 140, "endColumn": 14}, {"ruleId": "2076", "severity": 1, "message": "2370", "line": 141, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 141, "endColumn": 28}, {"ruleId": "2076", "severity": 1, "message": "2371", "line": 142, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 142, "endColumn": 22}, {"ruleId": "2076", "severity": 1, "message": "2385", "line": 266, "column": 11, "nodeType": "2078", "messageId": "2079", "endLine": 266, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2386", "line": 267, "column": 11, "nodeType": "2078", "messageId": "2079", "endLine": 267, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2387", "line": 471, "column": 17, "nodeType": "2078", "messageId": "2079", "endLine": 471, "endColumn": 28}, {"ruleId": "2076", "severity": 1, "message": "2301", "line": 472, "column": 20, "nodeType": "2078", "messageId": "2079", "endLine": 472, "endColumn": 30}, {"ruleId": "2076", "severity": 1, "message": "2388", "line": 472, "column": 32, "nodeType": "2078", "messageId": "2079", "endLine": 472, "endColumn": 38}, {"ruleId": "2076", "severity": 1, "message": "2389", "line": 485, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 485, "endColumn": 32}, {"ruleId": "2076", "severity": 1, "message": "2360", "line": 2, "column": 23, "nodeType": "2078", "messageId": "2079", "endLine": 2, "endColumn": 39}, {"ruleId": "2076", "severity": 1, "message": "2390", "line": 2, "column": 41, "nodeType": "2078", "messageId": "2079", "endLine": 2, "endColumn": 48}, {"ruleId": "2076", "severity": 1, "message": "2272", "line": 3, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 3, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2391", "line": 3, "column": 23, "nodeType": "2078", "messageId": "2079", "endLine": 3, "endColumn": 29}, {"ruleId": "2076", "severity": 1, "message": "2336", "line": 7, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 7, "endColumn": 14}, {"ruleId": "2076", "severity": 1, "message": "2392", "line": 78, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 78, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2393", "line": 81, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 81, "endColumn": 20}, {"ruleId": "2076", "severity": 1, "message": "2394", "line": 100, "column": 9, "nodeType": "2078", "messageId": "2079", "endLine": 100, "endColumn": 29}, {"ruleId": "2076", "severity": 1, "message": "2395", "line": 5, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 5, "endColumn": 18}, {"ruleId": "2076", "severity": 1, "message": "2350", "line": 7, "column": 3, "nodeType": "2078", "messageId": "2079", "endLine": 7, "endColumn": 10}, {"ruleId": "2076", "severity": 1, "message": "2396", "line": 11, "column": 3, "nodeType": "2078", "messageId": "2079", "endLine": 11, "endColumn": 14}, {"ruleId": "2076", "severity": 1, "message": "2397", "line": 12, "column": 3, "nodeType": "2078", "messageId": "2079", "endLine": 12, "endColumn": 12}, {"ruleId": "2076", "severity": 1, "message": "2398", "line": 69, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 69, "endColumn": 15}, {"ruleId": "2076", "severity": 1, "message": "2399", "line": 199, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 199, "endColumn": 18}, {"ruleId": "2076", "severity": 1, "message": "2400", "line": 288, "column": 7, "nodeType": "2078", "messageId": "2079", "endLine": 288, "endColumn": 21}, {"ruleId": "2076", "severity": 1, "message": "2401", "line": 5, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 5, "endColumn": 14}, {"ruleId": "2076", "severity": 1, "message": "2276", "line": 6, "column": 8, "nodeType": "2078", "messageId": "2079", "endLine": 6, "endColumn": 13}, {"ruleId": "2065", "severity": 1, "message": "2402", "line": 244, "column": 6, "nodeType": "2067", "endLine": 244, "endColumn": 16, "suggestions": "2403"}, {"ruleId": "2076", "severity": 1, "message": "2272", "line": 17, "column": 50, "nodeType": "2078", "messageId": "2079", "endLine": 17, "endColumn": 61}, {"ruleId": "2076", "severity": 1, "message": "2218", "line": 22, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 22, "endColumn": 19}, {"ruleId": "2076", "severity": 1, "message": "2404", "line": 151, "column": 10, "nodeType": "2078", "messageId": "2079", "endLine": 151, "endColumn": 21}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'page'. Either include it or remove the dependency array.", "ArrayExpression", ["2405"], ["2406"], "React Hook useEffect has missing dependencies: 'isContrastMode' and 'page.classList'. Either include them or remove the dependency array.", ["2407"], ["2408"], "React Hook useEffect has missing dependencies: 'page.dataset', 'page.style', 'savePreferences', and 'stopTransition'. Either include them or remove the dependency array. Outer scope values like 'window.devicePixelRatio' aren't valid dependencies because mutating them doesn't re-render the component.", ["2409"], ["2410"], "no-unused-vars", "'getNotificationsOfAdmin' is defined but never used.", "Identifier", "unusedVar", "'getNotificationsOfNurse' is defined but never used.", "'DashboardA' is assigned a value but never used.", "'DashboardB' is assigned a value but never used.", "'DashboardC' is assigned a value but never used.", "'DashboardD' is assigned a value but never used.", "'DashboardE' is assigned a value but never used.", "'DashboardF' is assigned a value but never used.", "'DashboardG' is assigned a value but never used.", "'DashboardH' is assigned a value but never used.", "'DashboardI' is assigned a value but never used.", "'DashboardJ' is assigned a value but never used.", "'DashboardK' is assigned a value but never used.", "'DoctorAppointments' is assigned a value but never used.", "'Tests' is assigned a value but never used.", "'DoctorsReviews' is assigned a value but never used.", "'PatientReviews' is assigned a value but never used.", "'Finances' is assigned a value but never used.", "'PageNotFound' is assigned a value but never used.", "'AddCaregiver' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2411"], ["2412"], "React Hook useEffect has a missing dependency: 'user.role'. Either include it or remove the dependency array.", ["2413"], "'getDoc' is defined but never used.", "'rows' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'Phone' is defined but never used.", "'PhoneNumberInput' is defined but never used.", "'PropTypes' is defined but never used.", "'StyledField' is assigned a value but never used.", "'GENDER_SELECT' is assigned a value but never used.", "'handleSubmit' is assigned a value but never used.", "'control' is assigned a value but never used.", "'errors' is assigned a value but never used.", "'isSubmitting' is assigned a value but never used.", "'watch' is assigned a value but never used.", "'onSelectImage' is defined but never used.", "'submitForm' is defined but never used.", "React Hook useEffect has missing dependencies: 'caregiver_id', 'caregivers', and 'setValue'. Either include them or remove the dependency array.", ["2414"], "'FormControl' is defined but never used.", "'DocPreview' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'current_user', 'setValue', and 'user_id'. Either include them or remove the dependency array.", ["2415"], "React Hook useEffect has missing dependencies: 'current_user?.role', 'current_user?.type', and 'setValue'. Either include them or remove the dependency array.", ["2416"], "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "unexpected", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "'app' is defined but never used.", "'getMessaging' is defined but never used.", "React Hook useEffect has a missing dependency: 'getPermission'. Either include it or remove the dependency array.", ["2417"], "React Hook useEffect has a missing dependency: 'chat_id'. Either include it or remove the dependency array.", ["2418"], "'AdminPanelSettings' is defined but never used.", "'useEffect' is defined but never used.", "'IconButton' is defined but never used.", "'ArrowBack' is defined but never used.", "'TaskListIcon' is defined but never used.", "'CalendarIcon' is defined but never used.", "'isRecurringAppointment' is defined but never used.", "'Mood' is defined but never used.", "'mobility' is defined but never used.", "'appitete' is defined but never used.", "'RedShifInfoCard' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "'nextAppointment' is assigned a value but never used.", "'seriesInfo' is assigned a value but never used.", "'handleBackToCalendar' is assigned a value but never used.", "'DailyAppointmentChart' is defined but never used.", "'PatientOverallAppointments' is defined but never used.", "'PatientsRadialBar' is defined but never used.", "'ConfirmedDiagnoses' is defined but never used.", "'DailyAppointmentsByDoc' is defined but never used.", "'DiagnosesDonut' is defined but never used.", "'PatientAppointmentsHistory' is defined but never used.", "'RadarAreaChart' is defined but never used.", "'useWindowSize' is defined but never used.", "'useState' is defined but never used.", "'useSelector' is defined but never used.", "'nanoid' is defined but never used.", "React Hook useEffect has missing dependencies: 'currentMonth', 'scrollToTop', 'setDayIndex', 'setMonthIndex', and 'todayIndex'. Either include them or remove the dependency array.", ["2419"], ["2420"], "React Hook useEffect has a missing dependency: 'scrollToTop'. Either include it or remove the dependency array.", ["2421"], ["2422"], "'ContrastControl' is defined but never used.", "'LayoutControl' is defined but never used.", "'ScaleControl' is defined but never used.", "'DirectionControl' is defined but never used.", "'isDashboard' is assigned a value but never used.", "'CustomSelect' is defined but never used.", "'depsOptions' is defined but never used.", "'Item' is defined but never used.", "'setCategory' is assigned a value but never used.", "'TodosLegend' is defined but never used.", "'Btn' is defined but never used.", "'doc' is defined but never used.", "'setDoc' is defined but never used.", "'updateDoc' is defined but never used.", "'db' is defined but never used.", "'COLLECTIONS' is defined but never used.", "'addNewTaskDocAction' is defined but never used.", "'todos' is assigned a value but never used.", "'currentPatient' is assigned a value but never used.", "'currentCaregiver' is assigned a value but never used.", "'currentNurse' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'enqueueSnackbar' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'changeHandler', 'options', 'placeholder', and 'value'. Either include them or remove the dependency array. If 'changeHandler' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2423"], ["2424"], "'SelectPlaceholder' is defined but never used.", "'doctor' is defined but never used.", "'patient' is defined but never used.", "'Box' is defined but never used.", "'WidgetsLoader' is defined but never used.", "'setChatsAction' is defined but never used.", "'generateNames' is defined but never used.", "'drawList' is assigned a value but never used.", ["2425"], "'Reminder' is defined but never used.", "'ActionButton' is defined but never used.", "'ShapeButton' is defined but never used.", "'styled' is defined but never used.", "'colors' is defined but never used.", "'dark' is defined but never used.", "'flex' is defined but never used.", "'fonts' is defined but never used.", "'light' is defined but never used.", "'textSizes' is defined but never used.", "'theme' is defined but never used.", "'AppointmentChip' is defined but never used.", "'caregivers' is defined but never used.", "'placeholder' is defined but never used.", "'MonthNavigator' is defined but never used.", "'setMonth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setIndex'. Either include it or remove the dependency array.", ["2426"], ["2427"], ["2428"], ["2429"], "React Hook useEffect has missing dependencies: 'currentMonth' and 'setIndex'. Either include them or remove the dependency array.", ["2430"], ["2431"], "React Hook useEffect has a missing dependency: 'data'. Either include it or remove the dependency array.", ["2432"], ["2433"], "React Hook useLayoutEffect has a missing dependency: 'page'. Either include it or remove the dependency array.", ["2434"], ["2435"], "'RangePickerWrapper' is defined but never used.", "'AdapterMoment' is defined but never used.", "'LocalizationProvider' is defined but never used.", "'DatePicker' is defined but never used.", "'Emergency' is defined but never used.", "'QtyBadge' is defined but never used.", "'unreadCount' is assigned a value but never used.", "'Input' is defined but never used.", "'Label' is defined but never used.", "'Search' is defined but never used.", "'user' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'appointmentDate' is assigned a value but never used.", "'today' is assigned a value but never used.", "'Timestamp' is defined but never used.", "'startDateTime' is assigned a value but never used.", "'endDateTime' is assigned a value but never used.", "'register' is assigned a value but never used.", "'recurrenceType' is assigned a value but never used.", "'recurrenceEndDate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setValue'. Either include it or remove the dependency array.", ["2436"], "React Hook useEffect has missing dependencies: 'setValue' and 'watch'. Either include them or remove the dependency array.", ["2437"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", ["2438"], "React Hook useEffect has missing dependencies: 'defaultValues?.caregiver', 'isFromCalendar', and 'setValue'. Either include them or remove the dependency array.", ["2439"], ["2440"], "'CustomRating' is defined but never used.", "'EditBtn' is defined but never used.", "'ChatBtn' is defined but never used.", "'CheckCircle' is defined but never used.", "'BlockIcon' is defined but never used.", "'Info' is assigned a value but never used.", "'InputWrapper' is defined but never used.", "'Field' is defined but never used.", "'addTodo' is defined but never used.", "'toggleCollapse' is defined but never used.", "'tasksOptions' is defined but never used.", "'addDoc' is defined but never used.", "'collection' is defined but never used.", "'addNewTaskToList' is defined but never used.", "'createNewTaskDocThunk' is defined but never used.", "'setTaskDetailsAction' is defined but never used.", "'Button' is defined but never used.", "'Add' is defined but never used.", "'tasksDetails' is assigned a value but never used.", "'device' is assigned a value but never used.", "'getDocs' is defined but never used.", "React Hook useEffect has missing dependencies: 'activeChat' and 'current_chat'. Either include them or remove the dependency array.", ["2441"], "'query' is defined but never used.", "'where' is defined but never used.", "'writeBatch' is defined but never used.", "'Badge' is defined but never used.", "'DoctorPopup' is defined but never used.", "'doctorsOptions' is defined but never used.", "'events' is defined but never used.", "'disabled' is defined but never used.", "'ScheduleAppointmentModal' is defined but never used.", "'caregivers' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'patientOptions' and 'selectedPatient'. Either include them or remove the dependency array.", ["2442"], "React Hook useEffect has missing dependencies: 'searchParams' and 'setSearchParams'. Either include them or remove the dependency array.", ["2443"], "'isScheduleOpen' is assigned a value but never used.", "'setScheduleOpen' is assigned a value but never used.", "'now' is assigned a value but never used.", "'current' is assigned a value but never used.", "'useRef' is defined but never used.", "'popupOpen' is assigned a value but never used.", "'endDate' is assigned a value but never used.", "'startDate' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'MISSED'.", "ObjectExpression", "'Nav' is defined but never used.", "React Hook useLayoutEffect has a missing dependency: 'id'. Either include it or remove the dependency array. If 'setPoints' needs the current value of 'id', you can also switch to useReducer instead of useState and read 'id' in the reducer.", ["2444"], ["2445"], "'doctor' is assigned a value but never used.", "'menu' is defined but never used.", "'Img' is assigned a value but never used.", "'Text' is assigned a value but never used.", "'doc1jpg' is defined but never used.", "'doc1webp' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleScroll'. Either include it or remove the dependency array.", ["2446"], ["2447"], "'updateTaskAction' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'DialogContentText' is defined but never used.", "'pen' is defined but never used.", "'avatar' is assigned a value but never used.", "'moment' is defined but never used.", "'addMessage' is defined but never used.", "'updateChatAction' is defined but never used.", "'Popover' is defined but never used.", "'TextField' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'selectedCountry' is assigned a value but never used.", "'selectedCity' is assigned a value but never used.", "'cities' is assigned a value but never used.", "'setCities' is assigned a value but never used.", "'getCountriesOptions' is assigned a value but never used.", "'handleCountryChange' is assigned a value but never used.", "'options' is assigned a value but never used.", "'Divider' is defined but never used.", "React Hook useEffect has missing dependencies: 'defaultValues?.caregiver', 'defaultValues?.date', 'isFromCalendar', and 'setValue'. Either include them or remove the dependency array.", ["2448"], "React Hook useEffect has a missing dependency: 'init'. Either include it or remove the dependency array.", ["2449"], ["2450"], "React Hook useEffect has missing dependencies: 'dispatch', 'notifications', and 'user?.role'. Either include them or remove the dependency array.", ["2451"], "'setValue' is assigned a value but never used.", "'docRef' is assigned a value but never used.", "'CircularProgress' is defined but never used.", "'breakpoints' is defined but never used.", "'AppointmentsHeadingIcon' is defined but never used.", "'TabContainer' is defined but never used.", "'TabNav' is defined but never used.", "'Label' is assigned a value but never used.", "'ReasonOfRequest' is assigned a value but never used.", "'GrayedInput' is assigned a value but never used.", "'caregiver' is assigned a value but never used.", "'nurse' is assigned a value but never used.", "'client_appointments' is assigned a value but never used.", "'findCaregiver' is assigned a value but never used.", "'renderGender' is defined but never used.", "'client_id' is assigned a value but never used.", ["2452"], "'searchParams' is assigned a value but never used.", "'setSearchParams' is assigned a value but never used.", ["2453"], "React Hook useEffect has missing dependencies: 'currentPatient?.bloodPressure', 'currentPatient?.bloodSugar', 'currentPatient?.id', 'currentPatient?.medAdminRequired', 'currentPatient?.needLabs', 'currentPatient?.pulse', 'currentPatient?.recommendedServices', 'currentPatient?.spo2', 'currentPatient?.temperature', 'currentPatient?.wounds', and 'setValue'. Either include them or remove the dependency array.", ["2454"], ["2455"], "The 'upcomingAppointments' logical expression could make the dependencies of useMemo Hook (at line 74) change on every render. Move it inside the useMemo callback. Alternatively, wrap the initialization of 'upcomingAppointments' in its own useMemo() Hook.", "VariableDeclarator", "'StaffStatus' is assigned a value but never used.", "'AppointmentListItem' is defined but never used.", "'firstName' is assigned a value but never used.", "'lastName' is assigned a value but never used.", "'logged_user' is assigned a value but never used.", "'nurses' is assigned a value but never used.", "'sortedStaffAppointments' is assigned a value but never used.", "'Tooltip' is defined but never used.", "'Cancel' is defined but never used.", "'canComplete' is assigned a value but never used.", "'tooltipText' is assigned a value but never used.", "'handleAddAppointment' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'CardContent' is defined but never used.", "'CardMedia' is defined but never used.", "'InfoCard' is assigned a value but never used.", "'ServiceLeft' is assigned a value but never used.", "'SignatureTitle' is assigned a value but never used.", "'Grid' is defined but never used.", "React Hook useEffect has missing dependencies: 'addressComponents.addressLine2' and 'onChange'. Either include them or remove the dependency array. If 'onChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2456"], "'copiedField' is assigned a value but never used.", {"desc": "2457", "fix": "2458"}, {"kind": "2459", "justification": "2460"}, {"desc": "2461", "fix": "2462"}, {"kind": "2459", "justification": "2460"}, {"desc": "2463", "fix": "2464"}, {"kind": "2459", "justification": "2460"}, {"desc": "2465", "fix": "2466"}, {"desc": "2467", "fix": "2468"}, {"desc": "2469", "fix": "2470"}, {"desc": "2471", "fix": "2472"}, {"desc": "2473", "fix": "2474"}, {"desc": "2475", "fix": "2476"}, {"desc": "2477", "fix": "2478"}, {"desc": "2479", "fix": "2480"}, {"desc": "2481", "fix": "2482"}, {"kind": "2459", "justification": "2460"}, {"desc": "2483", "fix": "2484"}, {"kind": "2459", "justification": "2460"}, {"desc": "2485", "fix": "2486"}, {"kind": "2459", "justification": "2460"}, {"desc": "2487", "fix": "2488"}, {"desc": "2489", "fix": "2490"}, {"kind": "2459", "justification": "2460"}, {"desc": "2489", "fix": "2491"}, {"kind": "2459", "justification": "2460"}, {"desc": "2492", "fix": "2493"}, {"kind": "2459", "justification": "2460"}, {"desc": "2494", "fix": "2495"}, {"kind": "2459", "justification": "2460"}, {"desc": "2496", "fix": "2497"}, {"kind": "2459", "justification": "2460"}, {"desc": "2498", "fix": "2499"}, {"desc": "2500", "fix": "2501"}, {"desc": "2500", "fix": "2502"}, {"desc": "2503", "fix": "2504"}, {"desc": "2500", "fix": "2505"}, {"desc": "2506", "fix": "2507"}, {"desc": "2508", "fix": "2509"}, {"desc": "2510", "fix": "2511"}, {"desc": "2512", "fix": "2513"}, {"kind": "2459", "justification": "2460"}, {"desc": "2514", "fix": "2515"}, {"kind": "2459", "justification": "2460"}, {"desc": "2516", "fix": "2517"}, {"desc": "2518", "fix": "2519"}, {"kind": "2459", "justification": "2460"}, {"desc": "2520", "fix": "2521"}, {"desc": "2522", "fix": "2523"}, {"desc": "2522", "fix": "2524"}, {"desc": "2525", "fix": "2526"}, {"desc": "2522", "fix": "2527"}, {"desc": "2528", "fix": "2529"}, "Update the dependencies array to be: [direction, page]", {"range": "2530", "text": "2531"}, "directive", "", "Update the dependencies array to be: [isContrastMode, page.classList]", {"range": "2532", "text": "2533"}, "Update the dependencies array to be: [isDarkMode, isContrastMode, fontScale, direction, page.style, page.dataset, savePreferences, stopTransition]", {"range": "2534", "text": "2535"}, "Update the dependencies array to be: [dispatch]", {"range": "2536", "text": "2537"}, "Update the dependencies array to be: [dispatch, user]", {"range": "2538", "text": "2539"}, "Update the dependencies array to be: [user.id, dispatch, enqueueSnackbar, user.role]", {"range": "2540", "text": "2541"}, "Update the dependencies array to be: [caregiver_id, caregivers, caregivers.length, setValue]", {"range": "2542", "text": "2543"}, "Update the dependencies array to be: [all_users.length, current_user, setValue, user_id]", {"range": "2544", "text": "2545"}, "Update the dependencies array to be: [activeTab, current_user?.role, current_user?.type, setValue]", {"range": "2546", "text": "2547"}, "Update the dependencies array to be: [getPermission]", {"range": "2548", "text": "2549"}, "Update the dependencies array to be: [activeList, chat_id]", {"range": "2550", "text": "2551"}, "Update the dependencies array to be: [currentMonth, scrollToTop, selectedTab, setDayIndex, setMonthIndex, todayIndex]", {"range": "2552", "text": "2553"}, "Update the dependencies array to be: [dayIndex, monthIndex, scrollToTop]", {"range": "2554", "text": "2555"}, "Update the dependencies array to be: [changeHandler, options, placeholder, value]", {"range": "2556", "text": "2557"}, "Update the dependencies array to be: [dispatch, logged_in_user?.id]", {"range": "2558", "text": "2559"}, "Update the dependencies array to be: [setIndex, todayIndex]", {"range": "2560", "text": "2561"}, {"range": "2562", "text": "2561"}, "Update the dependencies array to be: [currentMonth, setIndex]", {"range": "2563", "text": "2564"}, "Update the dependencies array to be: [data, index]", {"range": "2565", "text": "2566"}, "Update the dependencies array to be: [page]", {"range": "2567", "text": "2568"}, "Update the dependencies array to be: [date, setValue]", {"range": "2569", "text": "2570"}, "Update the dependencies array to be: [setValue, watch]", {"range": "2571", "text": "2572"}, {"range": "2573", "text": "2572"}, "Update the dependencies array to be: [defaultValues?.caregiver, isFromCalendar, open, setValue]", {"range": "2574", "text": "2575"}, {"range": "2576", "text": "2572"}, "Update the dependencies array to be: [activeChat, current_chat, user]", {"range": "2577", "text": "2578"}, "Update the dependencies array to be: [clients, patientOptions, searchParams, selectedPatient]", {"range": "2579", "text": "2580"}, "Update the dependencies array to be: [searchParams, selectedPatient, setSearchParams]", {"range": "2581", "text": "2582"}, "Update the dependencies array to be: [id, width]", {"range": "2583", "text": "2584"}, "Update the dependencies array to be: [ref, callback, contentHeight, handleScroll]", {"range": "2585", "text": "2586"}, "Update the dependencies array to be: [defaultValues?.caregiver, defaultValues?.date, isFromCalendar, isVisible, setValue]", {"range": "2587", "text": "2588"}, "Update the dependencies array to be: [init]", {"range": "2589", "text": "2590"}, "Update the dependencies array to be: [dispatch, notifications, user?.id, user?.role]", {"range": "2591", "text": "2592"}, "Update the dependencies array to be: [currentPatient, setValue]", {"range": "2593", "text": "2594"}, {"range": "2595", "text": "2594"}, "Update the dependencies array to be: [currentPatient?.bloodPressure, currentPatient?.bloodSugar, currentPatient?.id, currentPatient?.medAdminRequired, currentPatient?.needLabs, currentPatient?.pulse, currentPatient?.recommendedServices, currentPatient?.spo2, currentPatient?.temperature, currentPatient?.wounds, setValue]", {"range": "2596", "text": "2597"}, {"range": "2598", "text": "2594"}, "Update the dependencies array to be: [addressComponents.addressLine2, onChange, placeVal]", {"range": "2599", "text": "2600"}, [1425, 1436], "[direction, page]", [1607, 1609], "[isContrastMode, page.classList]", [2304, 2379], "[isDarkMode, isContrastMode, fontScale, direction, page.style, page.dataset, savePreferences, stopTransition]", [3805, 3807], "[dispatch]", [4542, 4548], "[dispatch, user]", [6115, 6152], "[user.id, dispatch, enqueueSnackbar, user.role]", [4933, 4953], "[caregiver_id, caregivers, caregivers.length, setValue]", [19640, 19659], "[all_users.length, current_user, setValue, user_id]", [19874, 19885], "[activeTab, current_user?.role, current_user?.type, setValue]", [6835, 6837], "[getPermission]", [1407, 1419], "[activeList, chat_id]", [2768, 2781], "[current<PERSON><PERSON><PERSON>, scrollT<PERSON><PERSON><PERSON>, selectedTab, setDayIndex, setMonthIndex, todayIndex]", [2905, 2927], "[dayIndex, monthIndex, scrollToTop]", [618, 620], "[changeHandler, options, placeholder, value]", [3482, 3502], "[dispatch, logged_in_user?.id]", [2037, 2049], "[setIndex, todayIndex]", [1416, 1428], [1136, 1138], "[current<PERSON><PERSON><PERSON>, setIndex]", [1480, 1487], "[data, index]", [590, 592], "[page]", [26482, 26488], "[date, setValue]", [27336, 27360], "[set<PERSON><PERSON><PERSON>, watch]", [27505, 27535], [27716, 27722], "[defaultValues?.caregiver, isFromCalendar, open, setValue]", [28983, 29071], [2276, 2282], "[activeChat, current_chat, user]", [4565, 4588], "[clients, patientOptions, searchParams, selectedPatient]", [4928, 4945], "[searchPara<PERSON>, selectedPatient, setSearchParams]", [882, 889], "[id, width]", [1578, 1608], "[ref, callback, contentHeight, handleScroll]", [12543, 12554], "[defaultValues?.caregiver, defaultValues?.date, isFromCalendar, isVisible, setValue]", [1927, 1929], "[init]", [3467, 3478], "[dispatch, notifications, user?.id, user?.role]", [14400, 14416], "[current<PERSON><PERSON><PERSON>, setValue]", [7722, 7738], [10147, 10149], "[currentPatient?.bloodPressure, currentPatient?.bloodSugar, currentPatient?.id, currentPatient?.medAdminRequired, currentPatient?.needLabs, currentPatient?.pulse, currentPatient?.recommendedServices, currentPatient?.spo2, currentPatient?.temperature, currentPatient?.wounds, setValue]", [5785, 5801], [6144, 6154], "[addressComponents.addressLine2, onChange, placeVal]"]