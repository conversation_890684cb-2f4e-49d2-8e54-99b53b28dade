{"ast": null, "code": "var _jsxFileName = \"D:\\\\Softwares\\\\insyt-care\\\\insyt-care-web\\\\src\\\\pages\\\\Settings.jsx\";\n// components\nimport Page from \"@layout/Page\";\nimport UserSettings from \"@widgets/UserSettings\";\nimport NotificationSettings from \"@components/NotificationSettings\";\nimport { Grid } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  return /*#__PURE__*/_jsxDEV(Page, {\n    title: \"Settings\",\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(UserSettings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(NotificationSettings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["Page", "UserSettings", "NotificationSettings", "Grid", "jsxDEV", "_jsxDEV", "Settings", "title", "children", "container", "spacing", "item", "xs", "md", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Softwares/insyt-care/insyt-care-web/src/pages/Settings.jsx"], "sourcesContent": ["// components\r\nimport Page from \"@layout/Page\";\r\nimport UserSettings from \"@widgets/UserSettings\";\r\nimport NotificationSettings from \"@components/NotificationSettings\";\r\nimport { Grid } from \"@mui/material\";\r\n\r\nconst Settings = () => {\r\n  return (\r\n    <Page title=\"Settings\">\r\n      <Grid container spacing={3}>\r\n        <Grid item xs={12} md={8}>\r\n          <UserSettings />\r\n        </Grid>\r\n        <Grid item xs={12} md={4}>\r\n          <NotificationSettings />\r\n        </Grid>\r\n      </Grid>\r\n    </Page>\r\n  );\r\n};\r\n\r\nexport default Settings;\r\n"], "mappings": ";AAAA;AACA,OAAOA,IAAI,MAAM,cAAc;AAC/B,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,SAASC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACED,OAAA,CAACL,IAAI;IAACO,KAAK,EAAC,UAAU;IAAAC,QAAA,eACpBH,OAAA,CAACF,IAAI;MAACM,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAF,QAAA,gBACzBH,OAAA,CAACF,IAAI;QAACQ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACvBH,OAAA,CAACJ,YAAY;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACPZ,OAAA,CAACF,IAAI;QAACQ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACvBH,OAAA,CAACH,oBAAoB;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACC,EAAA,GAbIZ,QAAQ;AAed,eAAeA,QAAQ;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}