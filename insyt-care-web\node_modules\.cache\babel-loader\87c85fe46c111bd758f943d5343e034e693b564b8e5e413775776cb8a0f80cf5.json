{"ast": null, "code": "var _jsxFileName = \"D:\\\\Softwares\\\\insyt-care\\\\insyt-care-web\\\\src\\\\AppLayout.jsx\",\n  _s = $RefreshSig$();\n// utils\nimport { lazy } from \"react\";\n\n// components\nimport { Navigate, Route, Routes } from \"react-router-dom\";\n\n// hooks\nimport { useEffect } from \"react\";\nimport { auth, messaging } from \"config/firebase.config\";\nimport { onAuthStateChanged } from \"firebase/auth\";\nimport ProtectedRoutes from \"@layout/ProtectedRoutes\";\nimport PublicRoutes from \"@layout/PublicRoutes\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { getLoggedInUser } from \"@store/slices/auth\";\nimport { getAllCaregiversOfNurse, getAllPatientsOfNurse, getAllUsers } from \"@store/slices/users\";\nimport { getAllChats, setupChatsRealtime, cleanupChatListeners } from \"@store/slices/chats\";\nimport { getAllAppointments, getAllAppointmentsOfNurse } from \"@store/slices/appointments\";\nimport { getAllTasks } from \"@store/slices/tasks\";\nimport { getNotificationsOfAdmin, getNotificationsOfNurse } from \"@store/slices/notifications\";\nimport { onMessage } from \"firebase/messaging\";\nimport { subscribeToNotifications, cleanupSubscriptions, addNewNotificationAction } from \"@store/slices/notifications\";\nimport { useSnackbar } from \"notistack\";\n\n// pages\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Dashboard = /*#__PURE__*/lazy(_c = () => import(\"@pages/Dashboard\"));\n_c2 = Dashboard;\nconst DashboardA = /*#__PURE__*/lazy(_c3 = () => import(\"@pages/DashboardA\"));\n_c4 = DashboardA;\nconst DashboardB = /*#__PURE__*/lazy(_c5 = () => import(\"@pages/DashboardB\"));\n_c6 = DashboardB;\nconst DashboardC = /*#__PURE__*/lazy(_c7 = () => import(\"@pages/DashboardC\"));\n_c8 = DashboardC;\nconst DashboardD = /*#__PURE__*/lazy(_c9 = () => import(\"@pages/DashboardD\"));\n_c0 = DashboardD;\nconst DashboardE = /*#__PURE__*/lazy(_c1 = () => import(\"@pages/DashboardE\"));\n_c10 = DashboardE;\nconst DashboardF = /*#__PURE__*/lazy(_c11 = () => import(\"@pages/DashboardF\"));\n_c12 = DashboardF;\nconst DashboardG = /*#__PURE__*/lazy(_c13 = () => import(\"@pages/DashboardG\"));\n_c14 = DashboardG;\nconst DashboardH = /*#__PURE__*/lazy(_c15 = () => import(\"@pages/DashboardH\"));\n_c16 = DashboardH;\nconst DashboardI = /*#__PURE__*/lazy(_c17 = () => import(\"@pages/DashboardI\"));\n_c18 = DashboardI;\nconst DashboardJ = /*#__PURE__*/lazy(_c19 = () => import(\"@pages/DashboardJ\"));\n_c20 = DashboardJ;\nconst DashboardK = /*#__PURE__*/lazy(_c21 = () => import(\"@pages/DashboardK\"));\n_c22 = DashboardK;\nconst DoctorAppointments = /*#__PURE__*/lazy(_c23 = () => import(\"@pages/DoctorAppointments\"));\n_c24 = DoctorAppointments;\nconst Appointments = /*#__PURE__*/lazy(_c25 = () => import(\"@pages/Appointments\"));\n_c26 = Appointments;\nconst AppointmentDetails = /*#__PURE__*/lazy(_c27 = () => import(\"@pages/AppointmentDetails\"));\n_c28 = AppointmentDetails;\nconst Patients = /*#__PURE__*/lazy(_c29 = () => import(\"@pages/Patients\"));\n_c30 = Patients;\nconst PatientDetails = /*#__PURE__*/lazy(_c31 = () => import(\"@pages/PatientDetails\"));\n_c32 = PatientDetails;\nconst StaffDetails = /*#__PURE__*/lazy(_c33 = () => import(\"@pages/StaffDetails\"));\n_c34 = StaffDetails;\nconst AddPatient = /*#__PURE__*/lazy(_c35 = () => import(\"@pages/AddPatient\"));\n_c36 = AddPatient;\nconst Tests = /*#__PURE__*/lazy(_c37 = () => import(\"@pages/Tests\"));\n_c38 = Tests;\nconst Staff = /*#__PURE__*/lazy(_c39 = () => import(\"@pages/Staff\"));\n_c40 = Staff;\nconst StaffMessenger = /*#__PURE__*/lazy(_c41 = () => import(\"@pages/DoctorMessenger\"));\n_c42 = StaffMessenger;\nconst PatientMessenger = /*#__PURE__*/lazy(_c43 = () => import(\"@pages/PatientMessenger\"));\n_c44 = PatientMessenger;\nconst DoctorsReviews = /*#__PURE__*/lazy(_c45 = () => import(\"@pages/DoctorsReviews\"));\n_c46 = DoctorsReviews;\nconst PatientReviews = /*#__PURE__*/lazy(_c47 = () => import(\"@pages/PatientReviews\"));\n_c48 = PatientReviews;\nconst Finances = /*#__PURE__*/lazy(_c49 = () => import(\"@pages/Finances\"));\n_c50 = Finances;\nconst Settings = /*#__PURE__*/lazy(_c51 = () => import(\"@pages/Settings\"));\n_c52 = Settings;\nconst PageNotFound = /*#__PURE__*/lazy(_c53 = () => import(\"@pages/PageNotFound\"));\n_c54 = PageNotFound;\nconst Login = /*#__PURE__*/lazy(_c55 = () => import(\"@pages/Login\"));\n_c56 = Login;\nconst Nurses = /*#__PURE__*/lazy(_c57 = () => import(\"@pages/Nurses\"));\n_c58 = Nurses;\nconst AddUser = /*#__PURE__*/lazy(_c59 = () => import(\"@pages/AddUser\"));\n_c60 = AddUser;\nconst AddCaregiver = /*#__PURE__*/lazy(_c61 = () => import(\"@pages/AddCaregiver\"));\n_c62 = AddCaregiver;\nconst Caregivers = /*#__PURE__*/lazy(_c63 = () => import(\"@pages/Caregivers\"));\n_c64 = Caregivers;\nconst Tasks = /*#__PURE__*/lazy(_c65 = () => import(\"@pages/Tasks\"));\n_c66 = Tasks;\nconst Notifications = /*#__PURE__*/lazy(_c67 = () => import(\"@pages/Notifications\"));\n_c68 = Notifications;\nconst AppLayout = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, currentUser => {\n      if (currentUser) {\n        dispatch(getLoggedInUser(currentUser.uid));\n      } else if (!currentUser) {\n        console.log(\"current.user >\", currentUser);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n  useEffect(() => {\n    if (user) {\n      // Set up real-time notification subscription for all users\n      dispatch(subscribeToNotifications({\n        userRole: user.role,\n        userId: user.id\n      }));\n      if ((user === null || user === void 0 ? void 0 : user.role) === \"ADMIN\") {\n        dispatch(getAllUsers());\n        dispatch(getAllAppointments());\n        dispatch(getAllTasks());\n      }\n      if ((user === null || user === void 0 ? void 0 : user.role) === \"NURSE\") {\n        dispatch(getAllChats(user === null || user === void 0 ? void 0 : user.id));\n        dispatch(getAllCaregiversOfNurse());\n        dispatch(getAllPatientsOfNurse(user === null || user === void 0 ? void 0 : user.id));\n        dispatch(getAllAppointmentsOfNurse(user === null || user === void 0 ? void 0 : user.id));\n        dispatch(getAllTasks());\n      }\n    }\n\n    // Cleanup function\n    return () => {\n      dispatch(cleanupSubscriptions());\n    };\n  }, [user]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.id) {\n      // Setup real-time listeners for chats\n      dispatch(setupChatsRealtime(user.id));\n\n      // Cleanup function\n      return () => {\n        dispatch(cleanupChatListeners());\n      };\n    }\n  }, [user === null || user === void 0 ? void 0 : user.id, dispatch]);\n\n  // Setup FCM foreground message listener\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.id) {\n      const unsubscribe = onMessage(messaging, payload => {\n        console.log('Foreground message received:', payload);\n\n        // Show browser notification if permission is granted\n        if (Notification.permission === 'granted') {\n          var _payload$notification;\n          enqueueSnackbar(((_payload$notification = payload.notification) === null || _payload$notification === void 0 ? void 0 : _payload$notification.body) || 'New notification received', {\n            variant: 'info',\n            autoHideDuration: 5000\n          });\n        }\n\n        // Add notification to Redux store\n        if (payload.data) {\n          var _payload$notification2, _payload$notification3;\n          const notificationData = {\n            id: payload.data.notificationId || Date.now().toString(),\n            title: ((_payload$notification2 = payload.notification) === null || _payload$notification2 === void 0 ? void 0 : _payload$notification2.title) || payload.data.title,\n            body: ((_payload$notification3 = payload.notification) === null || _payload$notification3 === void 0 ? void 0 : _payload$notification3.body) || payload.data.body,\n            type: payload.data.type || 'GENERAL',\n            read: false,\n            createdAt: {\n              toMillis: () => Date.now()\n            },\n            role: user.role,\n            receiverIds: [user.id],\n            metadata: payload.data\n          };\n          dispatch(addNewNotificationAction(notificationData));\n        }\n      });\n      return () => {\n        if (unsubscribe) {\n          unsubscribe();\n        }\n      };\n    }\n  }, [user === null || user === void 0 ? void 0 : user.id, dispatch, enqueueSnackbar]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        element: /*#__PURE__*/_jsxDEV(PublicRoutes, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 25\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoutes, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 25\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/nurses\",\n          element: /*#__PURE__*/_jsxDEV(Nurses, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/staff\",\n          element: /*#__PURE__*/_jsxDEV(Staff, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/caregivers\",\n          element: /*#__PURE__*/_jsxDEV(Caregivers, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/add_staff\",\n          element: /*#__PURE__*/_jsxDEV(AddUser, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/add_client\",\n          element: /*#__PURE__*/_jsxDEV(AddPatient, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/settings\",\n          element: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/clients\",\n          element: /*#__PURE__*/_jsxDEV(Patients, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/clients/:clientId\",\n          element: /*#__PURE__*/_jsxDEV(PatientDetails, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 53\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/staff/:staffId\",\n          element: /*#__PURE__*/_jsxDEV(StaffDetails, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 50\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/appointments\",\n          element: /*#__PURE__*/_jsxDEV(Appointments, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/appointments/:appointmentId\",\n          element: /*#__PURE__*/_jsxDEV(AppointmentDetails, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 63\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/staff_messenger\",\n          element: /*#__PURE__*/_jsxDEV(StaffMessenger, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 51\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/client_messenger\",\n          element: /*#__PURE__*/_jsxDEV(PatientMessenger, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/nurse_chats\",\n          element: /*#__PURE__*/_jsxDEV(PatientMessenger, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/tasks\",\n          element: /*#__PURE__*/_jsxDEV(Tasks, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/notifications\",\n          element: /*#__PURE__*/_jsxDEV(Notifications, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(AppLayout, \"AurVg84bL4OEZ46KIVrDswevclk=\", false, function () {\n  return [useDispatch, useSelector, useSnackbar];\n});\n_c69 = AppLayout;\nexport default AppLayout;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50, _c51, _c52, _c53, _c54, _c55, _c56, _c57, _c58, _c59, _c60, _c61, _c62, _c63, _c64, _c65, _c66, _c67, _c68, _c69;\n$RefreshReg$(_c, \"Dashboard$lazy\");\n$RefreshReg$(_c2, \"Dashboard\");\n$RefreshReg$(_c3, \"DashboardA$lazy\");\n$RefreshReg$(_c4, \"DashboardA\");\n$RefreshReg$(_c5, \"DashboardB$lazy\");\n$RefreshReg$(_c6, \"DashboardB\");\n$RefreshReg$(_c7, \"DashboardC$lazy\");\n$RefreshReg$(_c8, \"DashboardC\");\n$RefreshReg$(_c9, \"DashboardD$lazy\");\n$RefreshReg$(_c0, \"DashboardD\");\n$RefreshReg$(_c1, \"DashboardE$lazy\");\n$RefreshReg$(_c10, \"DashboardE\");\n$RefreshReg$(_c11, \"DashboardF$lazy\");\n$RefreshReg$(_c12, \"DashboardF\");\n$RefreshReg$(_c13, \"DashboardG$lazy\");\n$RefreshReg$(_c14, \"DashboardG\");\n$RefreshReg$(_c15, \"DashboardH$lazy\");\n$RefreshReg$(_c16, \"DashboardH\");\n$RefreshReg$(_c17, \"DashboardI$lazy\");\n$RefreshReg$(_c18, \"DashboardI\");\n$RefreshReg$(_c19, \"DashboardJ$lazy\");\n$RefreshReg$(_c20, \"DashboardJ\");\n$RefreshReg$(_c21, \"DashboardK$lazy\");\n$RefreshReg$(_c22, \"DashboardK\");\n$RefreshReg$(_c23, \"DoctorAppointments$lazy\");\n$RefreshReg$(_c24, \"DoctorAppointments\");\n$RefreshReg$(_c25, \"Appointments$lazy\");\n$RefreshReg$(_c26, \"Appointments\");\n$RefreshReg$(_c27, \"AppointmentDetails$lazy\");\n$RefreshReg$(_c28, \"AppointmentDetails\");\n$RefreshReg$(_c29, \"Patients$lazy\");\n$RefreshReg$(_c30, \"Patients\");\n$RefreshReg$(_c31, \"PatientDetails$lazy\");\n$RefreshReg$(_c32, \"PatientDetails\");\n$RefreshReg$(_c33, \"StaffDetails$lazy\");\n$RefreshReg$(_c34, \"StaffDetails\");\n$RefreshReg$(_c35, \"AddPatient$lazy\");\n$RefreshReg$(_c36, \"AddPatient\");\n$RefreshReg$(_c37, \"Tests$lazy\");\n$RefreshReg$(_c38, \"Tests\");\n$RefreshReg$(_c39, \"Staff$lazy\");\n$RefreshReg$(_c40, \"Staff\");\n$RefreshReg$(_c41, \"StaffMessenger$lazy\");\n$RefreshReg$(_c42, \"StaffMessenger\");\n$RefreshReg$(_c43, \"PatientMessenger$lazy\");\n$RefreshReg$(_c44, \"PatientMessenger\");\n$RefreshReg$(_c45, \"DoctorsReviews$lazy\");\n$RefreshReg$(_c46, \"DoctorsReviews\");\n$RefreshReg$(_c47, \"PatientReviews$lazy\");\n$RefreshReg$(_c48, \"PatientReviews\");\n$RefreshReg$(_c49, \"Finances$lazy\");\n$RefreshReg$(_c50, \"Finances\");\n$RefreshReg$(_c51, \"Settings$lazy\");\n$RefreshReg$(_c52, \"Settings\");\n$RefreshReg$(_c53, \"PageNotFound$lazy\");\n$RefreshReg$(_c54, \"PageNotFound\");\n$RefreshReg$(_c55, \"Login$lazy\");\n$RefreshReg$(_c56, \"Login\");\n$RefreshReg$(_c57, \"Nurses$lazy\");\n$RefreshReg$(_c58, \"Nurses\");\n$RefreshReg$(_c59, \"AddUser$lazy\");\n$RefreshReg$(_c60, \"AddUser\");\n$RefreshReg$(_c61, \"AddCaregiver$lazy\");\n$RefreshReg$(_c62, \"AddCaregiver\");\n$RefreshReg$(_c63, \"Caregivers$lazy\");\n$RefreshReg$(_c64, \"Caregivers\");\n$RefreshReg$(_c65, \"Tasks$lazy\");\n$RefreshReg$(_c66, \"Tasks\");\n$RefreshReg$(_c67, \"Notifications$lazy\");\n$RefreshReg$(_c68, \"Notifications\");\n$RefreshReg$(_c69, \"AppLayout\");", "map": {"version": 3, "names": ["lazy", "Navigate", "Route", "Routes", "useEffect", "auth", "messaging", "onAuthStateChanged", "ProtectedRoutes", "PublicRoutes", "useDispatch", "useSelector", "getLoggedInUser", "getAllCaregiversOfNurse", "getAllPatientsOfNurse", "getAllUsers", "getAllChats", "setupChatsRealtime", "cleanupChatListeners", "getAllAppointments", "getAllAppointmentsOfNurse", "getAllTasks", "getNotificationsOfAdmin", "getNotificationsOfNurse", "onMessage", "subscribeToNotifications", "cleanupSubscriptions", "addNewNotificationAction", "useSnackbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Dashboard", "_c", "_c2", "DashboardA", "_c3", "_c4", "DashboardB", "_c5", "_c6", "DashboardC", "_c7", "_c8", "DashboardD", "_c9", "_c0", "DashboardE", "_c1", "_c10", "DashboardF", "_c11", "_c12", "DashboardG", "_c13", "_c14", "DashboardH", "_c15", "_c16", "DashboardI", "_c17", "_c18", "DashboardJ", "_c19", "_c20", "DashboardK", "_c21", "_c22", "DoctorAppointments", "_c23", "_c24", "Appointments", "_c25", "_c26", "AppointmentDetails", "_c27", "_c28", "Patients", "_c29", "_c30", "PatientDetails", "_c31", "_c32", "StaffDetails", "_c33", "_c34", "AddPatient", "_c35", "_c36", "Tests", "_c37", "_c38", "Staff", "_c39", "_c40", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c41", "_c42", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c43", "_c44", "DoctorsReviews", "_c45", "_c46", "PatientReviews", "_c47", "_c48", "Finances", "_c49", "_c50", "Settings", "_c51", "_c52", "PageNotFound", "_c53", "_c54", "<PERSON><PERSON>", "_c55", "_c56", "Nurses", "_c57", "_c58", "AddUser", "_c59", "_c60", "AddCaregiver", "_c61", "_c62", "Caregivers", "_c63", "_c64", "Tasks", "_c65", "_c66", "Notifications", "_c67", "_c68", "AppLayout", "_s", "dispatch", "user", "state", "enqueueSnackbar", "unsubscribe", "currentUser", "uid", "console", "log", "userRole", "role", "userId", "id", "payload", "Notification", "permission", "_payload$notification", "notification", "body", "variant", "autoHideDuration", "data", "_payload$notification2", "_payload$notification3", "notificationData", "notificationId", "Date", "now", "toString", "title", "type", "read", "createdAt", "<PERSON><PERSON><PERSON><PERSON>", "receiverIds", "metadata", "children", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "to", "_c69", "$RefreshReg$"], "sources": ["D:/Softwares/insyt-care/insyt-care-web/src/AppLayout.jsx"], "sourcesContent": ["// utils\r\nimport { lazy } from \"react\";\r\n\r\n// components\r\nimport { Navigate, Route, Routes } from \"react-router-dom\";\r\n\r\n// hooks\r\nimport { useEffect } from \"react\";\r\nimport { auth, messaging } from \"config/firebase.config\";\r\nimport { onAuthStateChanged } from \"firebase/auth\";\r\nimport ProtectedRoutes from \"@layout/ProtectedRoutes\";\r\nimport PublicRoutes from \"@layout/PublicRoutes\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { getLoggedInUser } from \"@store/slices/auth\";\r\nimport { getAllCaregiversOfNurse, getAllPatientsOfNurse, getAllUsers } from \"@store/slices/users\";\r\nimport { getAllChats, setupChatsRealtime, cleanupChatListeners } from \"@store/slices/chats\";\r\nimport { getAllAppointments, getAllAppointmentsOfNurse } from \"@store/slices/appointments\";\r\nimport { getAllTasks } from \"@store/slices/tasks\";\r\nimport { getNotificationsOfAdmin, getNotificationsOfNurse } from \"@store/slices/notifications\";\r\nimport { onMessage } from \"firebase/messaging\";\r\nimport { subscribeToNotifications, cleanupSubscriptions, addNewNotificationAction } from \"@store/slices/notifications\";\r\nimport { useSnackbar } from \"notistack\";\r\n\r\n// pages\r\nconst Dashboard = lazy(() => import(\"@pages/Dashboard\"));\r\nconst DashboardA = lazy(() => import(\"@pages/DashboardA\"));\r\nconst DashboardB = lazy(() => import(\"@pages/DashboardB\"));\r\nconst DashboardC = lazy(() => import(\"@pages/DashboardC\"));\r\nconst DashboardD = lazy(() => import(\"@pages/DashboardD\"));\r\nconst DashboardE = lazy(() => import(\"@pages/DashboardE\"));\r\nconst DashboardF = lazy(() => import(\"@pages/DashboardF\"));\r\nconst DashboardG = lazy(() => import(\"@pages/DashboardG\"));\r\nconst DashboardH = lazy(() => import(\"@pages/DashboardH\"));\r\nconst DashboardI = lazy(() => import(\"@pages/DashboardI\"));\r\nconst DashboardJ = lazy(() => import(\"@pages/DashboardJ\"));\r\nconst DashboardK = lazy(() => import(\"@pages/DashboardK\"));\r\nconst DoctorAppointments = lazy(() => import(\"@pages/DoctorAppointments\"));\r\nconst Appointments = lazy(() => import(\"@pages/Appointments\"));\r\nconst AppointmentDetails = lazy(() => import(\"@pages/AppointmentDetails\"));\r\nconst Patients = lazy(() => import(\"@pages/Patients\"));\r\nconst PatientDetails = lazy(() => import(\"@pages/PatientDetails\"));\r\nconst StaffDetails = lazy(() => import(\"@pages/StaffDetails\"));\r\nconst AddPatient = lazy(() => import(\"@pages/AddPatient\"));\r\nconst Tests = lazy(() => import(\"@pages/Tests\"));\r\nconst Staff = lazy(() => import(\"@pages/Staff\"));\r\nconst StaffMessenger = lazy(() => import(\"@pages/DoctorMessenger\"));\r\nconst PatientMessenger = lazy(() => import(\"@pages/PatientMessenger\"));\r\nconst DoctorsReviews = lazy(() => import(\"@pages/DoctorsReviews\"));\r\nconst PatientReviews = lazy(() => import(\"@pages/PatientReviews\"));\r\nconst Finances = lazy(() => import(\"@pages/Finances\"));\r\nconst Settings = lazy(() => import(\"@pages/Settings\"));\r\nconst PageNotFound = lazy(() => import(\"@pages/PageNotFound\"));\r\nconst Login = lazy(() => import(\"@pages/Login\"));\r\nconst Nurses = lazy(() => import(\"@pages/Nurses\"));\r\nconst AddUser = lazy(() => import(\"@pages/AddUser\"));\r\nconst AddCaregiver = lazy(() => import(\"@pages/AddCaregiver\"));\r\nconst Caregivers = lazy(() => import(\"@pages/Caregivers\"));\r\nconst Tasks = lazy(() => import(\"@pages/Tasks\"));\r\nconst Notifications = lazy(() => import(\"@pages/Notifications\"));\r\n\r\nconst AppLayout = () => {\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.auth);\r\n  const { enqueueSnackbar } = useSnackbar();\r\n\r\n  useEffect(() => {\r\n    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {\r\n      if (currentUser) {\r\n        dispatch(getLoggedInUser(currentUser.uid));\r\n      } else if (!currentUser) {\r\n        console.log(\"current.user >\", currentUser);\r\n      }\r\n    });\r\n\r\n    return () => unsubscribe();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (user) {      // Set up real-time notification subscription for all users\r\n      dispatch(subscribeToNotifications({ userRole: user.role, userId: user.id }));\r\n\r\n      if (user?.role === \"ADMIN\") {\r\n        dispatch(getAllUsers());\r\n        dispatch(getAllAppointments());\r\n        dispatch(getAllTasks());\r\n      }\r\n      if (user?.role === \"NURSE\") {\r\n        dispatch(getAllChats(user?.id));\r\n        dispatch(getAllCaregiversOfNurse());\r\n        dispatch(getAllPatientsOfNurse(user?.id));\r\n        dispatch(getAllAppointmentsOfNurse(user?.id));\r\n        dispatch(getAllTasks());\r\n      }\r\n    }\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      dispatch(cleanupSubscriptions());\r\n    };\r\n  }, [user]);\r\n\r\n  useEffect(() => {\r\n    if (user?.id) {\r\n      // Setup real-time listeners for chats\r\n      dispatch(setupChatsRealtime(user.id));\r\n\r\n      // Cleanup function\r\n      return () => {\r\n        dispatch(cleanupChatListeners());\r\n      };\r\n    }\r\n  }, [user?.id, dispatch]);\r\n\r\n  // Setup FCM foreground message listener\r\n  useEffect(() => {\r\n    if (user?.id) {\r\n      const unsubscribe = onMessage(messaging, (payload) => {\r\n        console.log('Foreground message received:', payload);\r\n\r\n        // Show browser notification if permission is granted\r\n        if (Notification.permission === 'granted') {\r\n          enqueueSnackbar(payload.notification?.body || 'New notification received', {\r\n            variant: 'info',\r\n            autoHideDuration: 5000,\r\n          });\r\n        }\r\n\r\n        // Add notification to Redux store\r\n        if (payload.data) {\r\n          const notificationData = {\r\n            id: payload.data.notificationId || Date.now().toString(),\r\n            title: payload.notification?.title || payload.data.title,\r\n            body: payload.notification?.body || payload.data.body,\r\n            type: payload.data.type || 'GENERAL',\r\n            read: false,\r\n            createdAt: { toMillis: () => Date.now() },\r\n            role: user.role,\r\n            receiverIds: [user.id],\r\n            metadata: payload.data\r\n          };\r\n\r\n          dispatch(addNewNotificationAction(notificationData));\r\n        }\r\n      });\r\n\r\n      return () => {\r\n        if (unsubscribe) {\r\n          unsubscribe();\r\n        }\r\n      };\r\n    }\r\n  }, [user?.id, dispatch, enqueueSnackbar]);\r\n\r\n  return (\r\n    <>\r\n      <Routes>\r\n        {/* PUBLIC ROUTES */}\r\n        <Route element={<PublicRoutes />}>\r\n          <Route path=\"/\" element={<Navigate to=\"/login\" />} />\r\n          <Route path=\"/login\" element={<Login />} />\r\n        </Route>\r\n\r\n        {/* PROTECTED ROUTES */}\r\n        <Route element={<ProtectedRoutes />}>\r\n          <Route path=\"/dashboard\" element={<Dashboard />} />\r\n          <Route path=\"/nurses\" element={<Nurses />} />\r\n          <Route path=\"/staff\" element={<Staff />} />\r\n          <Route path=\"/caregivers\" element={<Caregivers />} />\r\n          <Route path=\"/add_staff\" element={<AddUser />} />\r\n          <Route path=\"/add_client\" element={<AddPatient />} />\r\n          <Route path=\"/settings\" element={<Settings />} />\r\n          <Route path=\"/clients\" element={<Patients />} />\r\n          <Route path=\"/clients/:clientId\" element={<PatientDetails />} />\r\n          <Route path=\"/staff/:staffId\" element={<StaffDetails />} />\r\n          <Route path=\"/appointments\" element={<Appointments />} />\r\n          <Route path=\"/appointments/:appointmentId\" element={<AppointmentDetails />} />\r\n          <Route path=\"/staff_messenger\" element={<StaffMessenger />} />\r\n          <Route path=\"/client_messenger\" element={<PatientMessenger />} />\r\n          <Route path=\"/nurse_chats\" element={<PatientMessenger />} />\r\n          <Route path=\"/tasks\" element={<Tasks />} />\r\n          <Route path=\"/notifications\" element={<Notifications />} />\r\n        </Route>\r\n      </Routes>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AppLayout;\r\n"], "mappings": ";;AAAA;AACA,SAASA,IAAI,QAAQ,OAAO;;AAE5B;AACA,SAASC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;;AAE1D;AACA,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,IAAI,EAAEC,SAAS,QAAQ,wBAAwB;AACxD,SAASC,kBAAkB,QAAQ,eAAe;AAClD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,uBAAuB,EAAEC,qBAAqB,EAAEC,WAAW,QAAQ,qBAAqB;AACjG,SAASC,WAAW,EAAEC,kBAAkB,EAAEC,oBAAoB,QAAQ,qBAAqB;AAC3F,SAASC,kBAAkB,EAAEC,yBAAyB,QAAQ,4BAA4B;AAC1F,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,uBAAuB,EAAEC,uBAAuB,QAAQ,6BAA6B;AAC9F,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,wBAAwB,EAAEC,oBAAoB,EAAEC,wBAAwB,QAAQ,6BAA6B;AACtH,SAASC,WAAW,QAAQ,WAAW;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,SAAS,gBAAGjC,IAAI,CAAAkC,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAACC,GAAA,GAAnDF,SAAS;AACf,MAAMG,UAAU,gBAAGpC,IAAI,CAAAqC,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAGvC,IAAI,CAAAwC,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAG1C,IAAI,CAAA2C,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAG7C,IAAI,CAAA8C,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAGhD,IAAI,CAAAiD,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAGnD,IAAI,CAAAoD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAGtD,IAAI,CAAAuD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAGzD,IAAI,CAAA0D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAG5D,IAAI,CAAA6D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAG/D,IAAI,CAAAgE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,UAAU,gBAAGlE,IAAI,CAAAmE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,kBAAkB,gBAAGrE,IAAI,CAAAsE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,IAAA,GAArEF,kBAAkB;AACxB,MAAMG,YAAY,gBAAGxE,IAAI,CAAAyE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAzDF,YAAY;AAClB,MAAMG,kBAAkB,gBAAG3E,IAAI,CAAA4E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,IAAA,GAArEF,kBAAkB;AACxB,MAAMG,QAAQ,gBAAG9E,IAAI,CAAA+E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAACC,IAAA,GAAjDF,QAAQ;AACd,MAAMG,cAAc,gBAAGjF,IAAI,CAAAkF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAACC,IAAA,GAA7DF,cAAc;AACpB,MAAMG,YAAY,gBAAGpF,IAAI,CAAAqF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAzDF,YAAY;AAClB,MAAMG,UAAU,gBAAGvF,IAAI,CAAAwF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,KAAK,gBAAG1F,IAAI,CAAA2F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,cAAc,CAAC,CAAC;AAACC,IAAA,GAA3CF,KAAK;AACX,MAAMG,KAAK,gBAAG7F,IAAI,CAAA8F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,cAAc,CAAC,CAAC;AAACC,IAAA,GAA3CF,KAAK;AACX,MAAMG,cAAc,gBAAGhG,IAAI,CAAAiG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAA9DF,cAAc;AACpB,MAAMG,gBAAgB,gBAAGnG,IAAI,CAAAoG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAAjEF,gBAAgB;AACtB,MAAMG,cAAc,gBAAGtG,IAAI,CAAAuG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAACC,IAAA,GAA7DF,cAAc;AACpB,MAAMG,cAAc,gBAAGzG,IAAI,CAAA0G,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAACC,IAAA,GAA7DF,cAAc;AACpB,MAAMG,QAAQ,gBAAG5G,IAAI,CAAA6G,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAACC,IAAA,GAAjDF,QAAQ;AACd,MAAMG,QAAQ,gBAAG/G,IAAI,CAAAgH,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAACC,IAAA,GAAjDF,QAAQ;AACd,MAAMG,YAAY,gBAAGlH,IAAI,CAAAmH,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAzDF,YAAY;AAClB,MAAMG,KAAK,gBAAGrH,IAAI,CAAAsH,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,cAAc,CAAC,CAAC;AAACC,IAAA,GAA3CF,KAAK;AACX,MAAMG,MAAM,gBAAGxH,IAAI,CAAAyH,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,eAAe,CAAC,CAAC;AAACC,IAAA,GAA7CF,MAAM;AACZ,MAAMG,OAAO,gBAAG3H,IAAI,CAAA4H,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAACC,IAAA,GAA/CF,OAAO;AACb,MAAMG,YAAY,gBAAG9H,IAAI,CAAA+H,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAzDF,YAAY;AAClB,MAAMG,UAAU,gBAAGjI,IAAI,CAAAkI,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAArDF,UAAU;AAChB,MAAMG,KAAK,gBAAGpI,IAAI,CAAAqI,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,cAAc,CAAC,CAAC;AAACC,IAAA,GAA3CF,KAAK;AACX,MAAMG,aAAa,gBAAGvI,IAAI,CAAAwI,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAA3DF,aAAa;AAEnB,MAAMG,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGlI,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmI;EAAK,CAAC,GAAGlI,WAAW,CAAEmI,KAAK,IAAKA,KAAK,CAACzI,IAAI,CAAC;EACnD,MAAM;IAAE0I;EAAgB,CAAC,GAAGnH,WAAW,CAAC,CAAC;EAEzCxB,SAAS,CAAC,MAAM;IACd,MAAM4I,WAAW,GAAGzI,kBAAkB,CAACF,IAAI,EAAG4I,WAAW,IAAK;MAC5D,IAAIA,WAAW,EAAE;QACfL,QAAQ,CAAChI,eAAe,CAACqI,WAAW,CAACC,GAAG,CAAC,CAAC;MAC5C,CAAC,MAAM,IAAI,CAACD,WAAW,EAAE;QACvBE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEH,WAAW,CAAC;MAC5C;IACF,CAAC,CAAC;IAEF,OAAO,MAAMD,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN5I,SAAS,CAAC,MAAM;IACd,IAAIyI,IAAI,EAAE;MAAO;MACfD,QAAQ,CAACnH,wBAAwB,CAAC;QAAE4H,QAAQ,EAAER,IAAI,CAACS,IAAI;QAAEC,MAAM,EAAEV,IAAI,CAACW;MAAG,CAAC,CAAC,CAAC;MAE5E,IAAI,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,MAAK,OAAO,EAAE;QAC1BV,QAAQ,CAAC7H,WAAW,CAAC,CAAC,CAAC;QACvB6H,QAAQ,CAACzH,kBAAkB,CAAC,CAAC,CAAC;QAC9ByH,QAAQ,CAACvH,WAAW,CAAC,CAAC,CAAC;MACzB;MACA,IAAI,CAAAwH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,MAAK,OAAO,EAAE;QAC1BV,QAAQ,CAAC5H,WAAW,CAAC6H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,EAAE,CAAC,CAAC;QAC/BZ,QAAQ,CAAC/H,uBAAuB,CAAC,CAAC,CAAC;QACnC+H,QAAQ,CAAC9H,qBAAqB,CAAC+H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,EAAE,CAAC,CAAC;QACzCZ,QAAQ,CAACxH,yBAAyB,CAACyH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,EAAE,CAAC,CAAC;QAC7CZ,QAAQ,CAACvH,WAAW,CAAC,CAAC,CAAC;MACzB;IACF;;IAEA;IACA,OAAO,MAAM;MACXuH,QAAQ,CAAClH,oBAAoB,CAAC,CAAC,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,CAACmH,IAAI,CAAC,CAAC;EAEVzI,SAAS,CAAC,MAAM;IACd,IAAIyI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEW,EAAE,EAAE;MACZ;MACAZ,QAAQ,CAAC3H,kBAAkB,CAAC4H,IAAI,CAACW,EAAE,CAAC,CAAC;;MAErC;MACA,OAAO,MAAM;QACXZ,QAAQ,CAAC1H,oBAAoB,CAAC,CAAC,CAAC;MAClC,CAAC;IACH;EACF,CAAC,EAAE,CAAC2H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,EAAE,EAAEZ,QAAQ,CAAC,CAAC;;EAExB;EACAxI,SAAS,CAAC,MAAM;IACd,IAAIyI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEW,EAAE,EAAE;MACZ,MAAMR,WAAW,GAAGxH,SAAS,CAAClB,SAAS,EAAGmJ,OAAO,IAAK;QACpDN,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEK,OAAO,CAAC;;QAEpD;QACA,IAAIC,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;UAAA,IAAAC,qBAAA;UACzCb,eAAe,CAAC,EAAAa,qBAAA,GAAAH,OAAO,CAACI,YAAY,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,IAAI,KAAI,2BAA2B,EAAE;YACzEC,OAAO,EAAE,MAAM;YACfC,gBAAgB,EAAE;UACpB,CAAC,CAAC;QACJ;;QAEA;QACA,IAAIP,OAAO,CAACQ,IAAI,EAAE;UAAA,IAAAC,sBAAA,EAAAC,sBAAA;UAChB,MAAMC,gBAAgB,GAAG;YACvBZ,EAAE,EAAEC,OAAO,CAACQ,IAAI,CAACI,cAAc,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;YACxDC,KAAK,EAAE,EAAAP,sBAAA,GAAAT,OAAO,CAACI,YAAY,cAAAK,sBAAA,uBAApBA,sBAAA,CAAsBO,KAAK,KAAIhB,OAAO,CAACQ,IAAI,CAACQ,KAAK;YACxDX,IAAI,EAAE,EAAAK,sBAAA,GAAAV,OAAO,CAACI,YAAY,cAAAM,sBAAA,uBAApBA,sBAAA,CAAsBL,IAAI,KAAIL,OAAO,CAACQ,IAAI,CAACH,IAAI;YACrDY,IAAI,EAAEjB,OAAO,CAACQ,IAAI,CAACS,IAAI,IAAI,SAAS;YACpCC,IAAI,EAAE,KAAK;YACXC,SAAS,EAAE;cAAEC,QAAQ,EAAEA,CAAA,KAAMP,IAAI,CAACC,GAAG,CAAC;YAAE,CAAC;YACzCjB,IAAI,EAAET,IAAI,CAACS,IAAI;YACfwB,WAAW,EAAE,CAACjC,IAAI,CAACW,EAAE,CAAC;YACtBuB,QAAQ,EAAEtB,OAAO,CAACQ;UACpB,CAAC;UAEDrB,QAAQ,CAACjH,wBAAwB,CAACyI,gBAAgB,CAAC,CAAC;QACtD;MACF,CAAC,CAAC;MAEF,OAAO,MAAM;QACX,IAAIpB,WAAW,EAAE;UACfA,WAAW,CAAC,CAAC;QACf;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,EAAE,EAAEZ,QAAQ,EAAEG,eAAe,CAAC,CAAC;EAEzC,oBACEjH,OAAA,CAAAE,SAAA;IAAAgJ,QAAA,eACElJ,OAAA,CAAC3B,MAAM;MAAA6K,QAAA,gBAELlJ,OAAA,CAAC5B,KAAK;QAAC+K,OAAO,eAAEnJ,OAAA,CAACrB,YAAY;UAAAyK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAL,QAAA,gBAC/BlJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,GAAG;UAACL,OAAO,eAAEnJ,OAAA,CAAC7B,QAAQ;YAACsL,EAAE,EAAC;UAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,QAAQ;UAACL,OAAO,eAAEnJ,OAAA,CAACuF,KAAK;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAGRvJ,OAAA,CAAC5B,KAAK;QAAC+K,OAAO,eAAEnJ,OAAA,CAACtB,eAAe;UAAA0K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAL,QAAA,gBAClClJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,YAAY;UAACL,OAAO,eAAEnJ,OAAA,CAACG,SAAS;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,SAAS;UAACL,OAAO,eAAEnJ,OAAA,CAAC0F,MAAM;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,QAAQ;UAACL,OAAO,eAAEnJ,OAAA,CAAC+D,KAAK;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,aAAa;UAACL,OAAO,eAAEnJ,OAAA,CAACmG,UAAU;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,YAAY;UAACL,OAAO,eAAEnJ,OAAA,CAAC6F,OAAO;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,aAAa;UAACL,OAAO,eAAEnJ,OAAA,CAACyD,UAAU;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,WAAW;UAACL,OAAO,eAAEnJ,OAAA,CAACiF,QAAQ;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,UAAU;UAACL,OAAO,eAAEnJ,OAAA,CAACgD,QAAQ;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,oBAAoB;UAACL,OAAO,eAAEnJ,OAAA,CAACmD,cAAc;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChEvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,iBAAiB;UAACL,OAAO,eAAEnJ,OAAA,CAACsD,YAAY;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,eAAe;UAACL,OAAO,eAAEnJ,OAAA,CAAC0C,YAAY;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,8BAA8B;UAACL,OAAO,eAAEnJ,OAAA,CAAC6C,kBAAkB;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9EvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,kBAAkB;UAACL,OAAO,eAAEnJ,OAAA,CAACkE,cAAc;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,mBAAmB;UAACL,OAAO,eAAEnJ,OAAA,CAACqE,gBAAgB;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjEvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,cAAc;UAACL,OAAO,eAAEnJ,OAAA,CAACqE,gBAAgB;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,QAAQ;UAACL,OAAO,eAAEnJ,OAAA,CAACsG,KAAK;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CvJ,OAAA,CAAC5B,KAAK;UAACoL,IAAI,EAAC,gBAAgB;UAACL,OAAO,eAAEnJ,OAAA,CAACyG,aAAa;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC,gBACT,CAAC;AAEP,CAAC;AAAC1C,EAAA,CA7HID,SAAS;EAAA,QACIhI,WAAW,EACXC,WAAW,EACAiB,WAAW;AAAA;AAAA4J,IAAA,GAHnC9C,SAAS;AA+Hf,eAAeA,SAAS;AAAC,IAAAxG,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAA+C,IAAA;AAAAC,YAAA,CAAAvJ,EAAA;AAAAuJ,YAAA,CAAAtJ,GAAA;AAAAsJ,YAAA,CAAApJ,GAAA;AAAAoJ,YAAA,CAAAnJ,GAAA;AAAAmJ,YAAA,CAAAjJ,GAAA;AAAAiJ,YAAA,CAAAhJ,GAAA;AAAAgJ,YAAA,CAAA9I,GAAA;AAAA8I,YAAA,CAAA7I,GAAA;AAAA6I,YAAA,CAAA3I,GAAA;AAAA2I,YAAA,CAAA1I,GAAA;AAAA0I,YAAA,CAAAxI,GAAA;AAAAwI,YAAA,CAAAvI,IAAA;AAAAuI,YAAA,CAAArI,IAAA;AAAAqI,YAAA,CAAApI,IAAA;AAAAoI,YAAA,CAAAlI,IAAA;AAAAkI,YAAA,CAAAjI,IAAA;AAAAiI,YAAA,CAAA/H,IAAA;AAAA+H,YAAA,CAAA9H,IAAA;AAAA8H,YAAA,CAAA5H,IAAA;AAAA4H,YAAA,CAAA3H,IAAA;AAAA2H,YAAA,CAAAzH,IAAA;AAAAyH,YAAA,CAAAxH,IAAA;AAAAwH,YAAA,CAAAtH,IAAA;AAAAsH,YAAA,CAAArH,IAAA;AAAAqH,YAAA,CAAAnH,IAAA;AAAAmH,YAAA,CAAAlH,IAAA;AAAAkH,YAAA,CAAAhH,IAAA;AAAAgH,YAAA,CAAA/G,IAAA;AAAA+G,YAAA,CAAA7G,IAAA;AAAA6G,YAAA,CAAA5G,IAAA;AAAA4G,YAAA,CAAA1G,IAAA;AAAA0G,YAAA,CAAAzG,IAAA;AAAAyG,YAAA,CAAAvG,IAAA;AAAAuG,YAAA,CAAAtG,IAAA;AAAAsG,YAAA,CAAApG,IAAA;AAAAoG,YAAA,CAAAnG,IAAA;AAAAmG,YAAA,CAAAjG,IAAA;AAAAiG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA7F,IAAA;AAAA6F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAzE,IAAA;AAAAyE,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAjD,IAAA;AAAAiD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}