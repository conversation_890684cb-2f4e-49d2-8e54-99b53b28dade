import { db } from '@config/firebase.config';
import { collection, addDoc, Timestamp } from 'firebase/firestore';
import { COLLECTIONS } from '@constants/app';

/**
 * Test function to send a notification to nurses
 * This helps debug the notification system
 */
export const sendTestNotificationToNurses = async () => {
  try {
    const testNotification = {
      title: "Test Notification",
      body: "This is a test notification to verify the push notification system is working for nurses.",
      role: "NURSE",
      type: "TEST",
      read: false,
      receiverIds: [], // Will be populated by cloud function based on role
      metadata: {
        isTest: true,
        sentAt: new Date().toISOString()
      },
      createdAt: Timestamp.now(),
    };

    const docRef = await addDoc(collection(db, COLLECTIONS.NOTIFICATIONS), testNotification);
    console.log("Test notification sent with ID:", docRef.id);
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error("Error sending test notification:", error);
    return { success: false, error: error.message };
  }
};

/**
 * Test function to send a notification to a specific user
 */
export const sendTestNotificationToUser = async (userId, userRole) => {
  try {
    const testNotification = {
      title: "Personal Test Notification",
      body: `This is a test notification sent specifically to you (${userRole}).`,
      role: userRole,
      type: "TEST_PERSONAL",
      read: false,
      receiverIds: [userId],
      metadata: {
        isTest: true,
        targetUserId: userId,
        sentAt: new Date().toISOString()
      },
      createdAt: Timestamp.now(),
    };

    const docRef = await addDoc(collection(db, COLLECTIONS.NOTIFICATIONS), testNotification);
    console.log("Personal test notification sent with ID:", docRef.id);
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error("Error sending personal test notification:", error);
    return { success: false, error: error.message };
  }
};

/**
 * Debug function to check user's FCM token and notification settings
 */
export const debugUserNotificationSettings = (user) => {
  console.log("=== User Notification Debug Info ===");
  console.log("User ID:", user?.id);
  console.log("User Role:", user?.role);
  console.log("FCM Token:", user?.fcmToken ? "Present" : "Missing");
  console.log("Push Notifications Enabled:", user?.pushNotification !== false);
  console.log("Browser Permission:", Notification.permission);
  console.log("FCM Token (first 20 chars):", user?.fcmToken?.substring(0, 20) + "...");
  console.log("=====================================");
  
  return {
    userId: user?.id,
    role: user?.role,
    hasFcmToken: !!user?.fcmToken,
    pushNotificationsEnabled: user?.pushNotification !== false,
    browserPermission: Notification.permission,
    fcmTokenPreview: user?.fcmToken?.substring(0, 20) + "..."
  };
};
