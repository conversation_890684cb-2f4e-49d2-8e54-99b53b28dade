import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Switch,
  FormControlLabel,
  Button,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import { useSelector } from 'react-redux';
import { doc, updateDoc } from 'firebase/firestore';
import { db, messaging } from '@config/firebase.config';
import { COLLECTIONS } from '@constants/app';
import { getToken, isSupported } from 'firebase/messaging';
import { useSnackbar } from 'notistack';

const NotificationSettings = () => {
  const { user } = useSelector((state) => state.auth);
  const { enqueueSnackbar } = useSnackbar();
  
  const [pushNotifications, setPushNotifications] = useState(true);
  const [browserPermission, setBrowserPermission] = useState('default');
  const [loading, setLoading] = useState(false);
  const [fcmSupported, setFcmSupported] = useState(false);

  useEffect(() => {
    // Check if FCM is supported
    isSupported().then(setFcmSupported);
    
    // Check browser notification permission
    setBrowserPermission(Notification.permission);
    
    // Set initial state from user data
    if (user) {
      setPushNotifications(user.pushNotification !== false); // Default to true
    }
  }, [user]);

  const handlePushNotificationToggle = async (event) => {
    const enabled = event.target.checked;
    setLoading(true);

    try {
      if (enabled) {
        // If enabling notifications, request permission and get FCM token
        if (Notification.permission !== 'granted') {
          const permission = await Notification.requestPermission();
          setBrowserPermission(permission);
          
          if (permission !== 'granted') {
            enqueueSnackbar('Browser notification permission is required for push notifications.', { 
              variant: 'warning' 
            });
            setLoading(false);
            return;
          }
        }

        // Get FCM token
        if (fcmSupported) {
          const vapidKey = process.env.REACT_APP_FIREBASE_WEB_PUSH;
          if (!vapidKey) {
            enqueueSnackbar('Push notification configuration is incomplete. Please contact administrator.', { 
              variant: 'error' 
            });
            setLoading(false);
            return;
          }

          const token = await getToken(messaging, { vapidKey });
          if (token) {
            // Update user document with FCM token and notification preference
            await updateDoc(doc(db, COLLECTIONS.USERS, user.id), {
              pushNotification: true,
              fcmToken: token,
              notificationUpdatedAt: new Date()
            });
            
            setPushNotifications(true);
            enqueueSnackbar('Push notifications enabled successfully!', { variant: 'success' });
          } else {
            throw new Error('Unable to get FCM token');
          }
        }
      } else {
        // If disabling notifications, just update the preference
        await updateDoc(doc(db, COLLECTIONS.USERS, user.id), {
          pushNotification: false,
          notificationUpdatedAt: new Date()
        });
        
        setPushNotifications(false);
        enqueueSnackbar('Push notifications disabled.', { variant: 'info' });
      }
    } catch (error) {
      console.error('Error updating notification settings:', error);
      enqueueSnackbar('Failed to update notification settings. Please try again.', { 
        variant: 'error' 
      });
    } finally {
      setLoading(false);
    }
  };

  const requestBrowserPermission = async () => {
    try {
      const permission = await Notification.requestPermission();
      setBrowserPermission(permission);
      
      if (permission === 'granted') {
        enqueueSnackbar('Browser notification permission granted!', { variant: 'success' });
      } else {
        enqueueSnackbar('Browser notification permission denied.', { variant: 'warning' });
      }
    } catch (error) {
      console.error('Error requesting permission:', error);
      enqueueSnackbar('Error requesting notification permission.', { variant: 'error' });
    }
  };

  if (!fcmSupported) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Notification Settings
          </Typography>
          <Alert severity="info">
            Push notifications are not supported in this browser.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Notification Settings
        </Typography>
        
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Manage your push notification preferences
          </Typography>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Browser Permission Status */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Browser Permission Status
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" color="text.secondary">
              {browserPermission === 'granted' && '✅ Granted'}
              {browserPermission === 'denied' && '❌ Denied'}
              {browserPermission === 'default' && '⏳ Not requested'}
            </Typography>
            {browserPermission !== 'granted' && (
              <Button 
                size="small" 
                variant="outlined" 
                onClick={requestBrowserPermission}
              >
                Request Permission
              </Button>
            )}
          </Box>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Push Notification Toggle */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="subtitle2">
              Push Notifications
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Receive notifications for new messages, appointments, and updates
            </Typography>
          </Box>
          <FormControlLabel
            control={
              <Switch
                checked={pushNotifications}
                onChange={handlePushNotificationToggle}
                disabled={loading || browserPermission === 'denied'}
              />
            }
            label=""
          />
        </Box>

        {loading && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 2 }}>
            <CircularProgress size={16} />
            <Typography variant="body2" color="text.secondary">
              Updating settings...
            </Typography>
          </Box>
        )}

        {browserPermission === 'denied' && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            Browser notifications are blocked. To enable push notifications, please allow notifications 
            in your browser settings and refresh the page.
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default NotificationSettings;
